# Multi-Currency Platform Database Schema

This document provides the complete database schema for the scalable multi-currency Al-Raya Store platform.

## Migration Overview

This migration transforms the hardcoded dual-currency system (SDG/EGP) into a configurable multi-currency platform with exchange rate support.

### Migration Strategy
1. **Phase 1**: Create new currency tables
2. **Phase 2**: Migrate existing data
3. **Phase 3**: Update constraints and add RLS policies
4. **Phase 4**: Create utility functions and triggers

## Core Currency Tables

### 1. `currencies` table
```sql
CREATE TABLE currencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(3) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  arabic_name VARCHAR(100),
  decimal_places INTEGER DEFAULT 2 CHECK (decimal_places >= 0 AND decimal_places <= 8),
  is_rtl BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. `wallet_transactions` table
```sql
CREATE TABLE wallet_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES user_wallets(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'purchase')),
  amount DECIMAL(15,2) NOT NULL,
  currency VARCHAR(3) NOT NULL CHECK (currency IN ('SDG', 'EGP')),
  description TEXT NOT NULL,
  reference VARCHAR(100),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. `user_preferences` table
```sql
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  preferred_currency VARCHAR(3) DEFAULT 'SDG' CHECK (preferred_currency IN ('SDG', 'EGP')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Component Integration Points

### 1. WalletPage.tsx
**Location**: `components/pages/WalletPage.tsx`

**Required Changes**:
- Replace `mockWalletData` with real-time Supabase subscription
- Implement user authentication check
- Add error handling for network failures

**Supabase Integration**:
```typescript
// Replace the mock data state with:
const { data: walletData, error, isLoading } = useWalletData(user?.id)
const { data: userPreferences } = useUserPreferences(user?.id)

// Add real-time subscription:
useEffect(() => {
  const subscription = supabase
    .channel('wallet_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'user_wallets' },
      (payload) => {
        // Update wallet data
      }
    )
    .subscribe()
  
  return () => subscription.unsubscribe()
}, [])
```

### 2. WalletBalance.tsx
**Location**: `components/wallet/WalletBalance.tsx`

**Integration Points**:
- Line 25: `getBalanceForCurrency()` - Replace with Supabase query
- Line 47: Balance display - Connect to real wallet data
- Line 67: Add balance button - Implement payment flow
- Line 95: Total purchases calculation - Query from transactions table

**Required Functions**:
```typescript
// Get balance for specific currency
const getWalletBalance = async (userId: string, currency: Currency) => {
  const { data, error } = await supabase
    .from('user_wallets')
    .select('balance')
    .eq('user_id', userId)
    .eq('currency', currency)
    .single()
  
  return data?.balance || 0
}

// Calculate total purchases
const getTotalPurchases = async (userId: string, currency: Currency) => {
  const { data, error } = await supabase
    .from('wallet_transactions')
    .select('amount')
    .eq('user_id', userId)
    .eq('currency', currency)
    .eq('type', 'purchase')
    .eq('status', 'completed')
  
  return data?.reduce((sum, t) => sum + t.amount, 0) || 0
}
```

### 3. CurrencySelector.tsx
**Location**: `components/wallet/CurrencySelector.tsx`

**Integration Points**:
- Line 21: Currency change handler - Save preference to Supabase
- Load user's preferred currency on component mount

**Required Functions**:
```typescript
// Update user currency preference
const updateCurrencyPreference = async (userId: string, currency: Currency) => {
  const { error } = await supabase
    .from('user_preferences')
    .upsert({ 
      user_id: userId, 
      preferred_currency: currency 
    })
  
  if (error) throw error
}
```

### 4. WalletTransactions.tsx
**Location**: `components/wallet/WalletTransactions.tsx`

**Integration Points**:
- Line 23: `getFilteredTransactions()` - Replace with Supabase queries
- Line 26: Transaction filtering - Use Supabase filters
- Line 29: Transaction sorting - Use Supabase ordering

**Required Functions**:
```typescript
// Get filtered transactions
const getTransactions = async (
  userId: string, 
  currency: Currency, 
  type?: string,
  limit: number = 50
) => {
  let query = supabase
    .from('wallet_transactions')
    .select('*')
    .eq('user_id', userId)
    .eq('currency', currency)
    .order('created_at', { ascending: false })
    .limit(limit)
  
  if (type) {
    query = query.eq('type', type)
  }
  
  const { data, error } = await query
  return data || []
}
```

## Real-time Features

### 1. Balance Updates
Implement real-time balance updates using Supabase subscriptions:

```typescript
useEffect(() => {
  const subscription = supabase
    .channel('balance_updates')
    .on('postgres_changes', {
      event: 'UPDATE',
      schema: 'public',
      table: 'user_wallets',
      filter: `user_id=eq.${user?.id}`
    }, (payload) => {
      // Update balance in state
      setWalletData(prev => ({
        ...prev,
        balances: prev.balances.map(b => 
          b.currency === payload.new.currency 
            ? { ...b, amount: payload.new.balance }
            : b
        )
      }))
    })
    .subscribe()

  return () => subscription.unsubscribe()
}, [user?.id])
```

### 2. Transaction Updates
Subscribe to new transactions:

```typescript
useEffect(() => {
  const subscription = supabase
    .channel('transaction_updates')
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'wallet_transactions',
      filter: `user_id=eq.${user?.id}`
    }, (payload) => {
      // Add new transaction to state
      setWalletData(prev => ({
        ...prev,
        transactions: [payload.new, ...prev.transactions]
      }))
    })
    .subscribe()

  return () => subscription.unsubscribe()
}, [user?.id])
```

## Security Considerations

### Row Level Security (RLS)
Enable RLS on all wallet tables:

```sql
-- Enable RLS
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can only access their own wallets" ON user_wallets
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own transactions" ON wallet_transactions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);
```

## Error Handling

Implement comprehensive error handling:

```typescript
// In components, wrap Supabase calls with try-catch
try {
  const balance = await getWalletBalance(user.id, selectedCurrency)
  setCurrentBalance(balance)
} catch (error) {
  console.error('Failed to fetch balance:', error)
  toast.error('فشل في تحميل الرصيد')
}
```

## Performance Optimization

1. **Pagination**: Implement pagination for transactions list
2. **Caching**: Use React Query or SWR for data caching
3. **Debouncing**: Debounce currency selector changes
4. **Lazy Loading**: Load transaction details on demand

## Testing

Create test cases for:
1. Balance fetching and display
2. Currency switching
3. Transaction filtering
4. Real-time updates
5. Error scenarios
6. Loading states

## Deployment Checklist

- [ ] Database tables created
- [ ] RLS policies enabled
- [ ] Supabase client configured
- [ ] Environment variables set
- [ ] Error handling implemented
- [ ] Real-time subscriptions tested
- [ ] Performance optimized
- [ ] Security reviewed
