"use client"

import { useState, useEffect } from "react"
import {
  WalletData,
  Currency,
  Transaction,
  WalletBalance,
  CurrencyConversion,
  CurrencyConversionRequest,
  UserCurrencyPreferences,
  CurrencyDisplay,
  TransactionDisplay
} from "@/lib/types"
import { mockWalletData } from "@/lib/data/mockWalletData"
import { getAvailableCurrencies } from "@/lib/data/currencies"

// ## Enhanced custom hook for multi-currency wallet data management
// ## Integrated with Supabase for real-time wallet operations and currency conversion
export function useWalletData(userId?: string) {
  const [walletData, setWalletData] = useState<WalletData>(mockWalletData)
  const [availableCurrencies, setAvailableCurrencies] = useState<CurrencyDisplay[]>([])
  const [conversionHistory, setConversionHistory] = useState<CurrencyConversion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // ## Fetch wallet data from Supabase
  const fetchWalletData = async () => {
    if (!userId) return

    setIsLoading(true)
    setError(null)

    try {
      // ## TODO: Replace with actual Supabase queries
      // const { data: balances } = await supabase
      //   .from('user_wallets')
      //   .select('*')
      //   .eq('user_id', userId)
      
      // const { data: transactions } = await supabase
      //   .from('wallet_transactions')
      //   .select('*')
      //   .eq('user_id', userId)
      //   .order('created_at', { ascending: false })
      //   .limit(50)

      // const { data: preferences } = await supabase
      //   .from('user_preferences')
      //   .select('preferred_currency')
      //   .eq('user_id', userId)
      //   .single()

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Fetch available currencies
      const currencies = await getAvailableCurrencies()
      setAvailableCurrencies(currencies)

      // For now, return enhanced mock data
      setWalletData({
        ...mockWalletData,
        availableCurrencies: currencies
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch wallet data')
    } finally {
      setIsLoading(false)
    }
  }

  // ## Update currency preference in Supabase
  const updateCurrencyPreference = async (currency: Currency) => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase update
      // await supabase
      //   .from('user_preferences')
      //   .upsert({ 
      //     user_id: userId, 
      //     preferred_currency: currency 
      //   })

      setWalletData(prev => ({
        ...prev,
        selectedCurrency: currency
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update currency preference')
    }
  }

  // ## Add new transaction (for deposits, purchases, etc.)
  const addTransaction = async (transaction: Omit<Transaction, 'id' | 'date'>) => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase insert
      // const { data } = await supabase
      //   .from('wallet_transactions')
      //   .insert({
      //     user_id: userId,
      //     ...transaction,
      //     created_at: new Date().toISOString()
      //   })
      //   .select()
      //   .single()

      const newTransaction: Transaction = {
        ...transaction,
        id: `txn_${Date.now()}`,
        date: new Date()
      }

      setWalletData(prev => ({
        ...prev,
        transactions: [newTransaction, ...prev.transactions]
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add transaction')
    }
  }

  // ## Update wallet balance after transaction
  const updateBalance = async (currency: Currency, amount: number, operation: 'add' | 'subtract') => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase update
      // await supabase.rpc('update_wallet_balance', {
      //   p_user_id: userId,
      //   p_currency: currency,
      //   p_amount: operation === 'add' ? amount : -amount
      // })

      setWalletData(prev => ({
        ...prev,
        balances: prev.balances.map(balance => 
          balance.currency === currency
            ? {
                ...balance,
                amount: operation === 'add' 
                  ? balance.amount + amount 
                  : balance.amount - amount,
                lastUpdated: new Date()
              }
            : balance
        )
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update balance')
    }
  }

  // ## Set up real-time subscriptions
  useEffect(() => {
    if (!userId) return

    // ## TODO: Set up Supabase real-time subscriptions
    // const balanceSubscription = supabase
    //   .channel('wallet_balance_changes')
    //   .on('postgres_changes', {
    //     event: '*',
    //     schema: 'public',
    //     table: 'user_wallets',
    //     filter: `user_id=eq.${userId}`
    //   }, (payload) => {
    //     // Handle balance updates
    //   })
    //   .subscribe()

    // const transactionSubscription = supabase
    //   .channel('wallet_transaction_changes')
    //   .on('postgres_changes', {
    //     event: 'INSERT',
    //     schema: 'public',
    //     table: 'wallet_transactions',
    //     filter: `user_id=eq.${userId}`
    //   }, (payload) => {
    //     // Handle new transactions
    //   })
    //   .subscribe()

    // return () => {
    //   balanceSubscription.unsubscribe()
    //   transactionSubscription.unsubscribe()
    // }

    fetchWalletData()
  }, [userId])

  // ## Currency conversion functions
  const convertCurrency = async (request: CurrencyConversionRequest): Promise<CurrencyConversion | null> => {
    if (!userId) return null

    setIsConverting(true)
    setError(null)

    try {
      // ## TODO: Replace with actual API call
      const response = await fetch('/api/wallet/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Conversion failed')
      }

      const conversion = data.conversion
      setConversionHistory(prev => [conversion, ...prev])

      // Update wallet balances
      setWalletData(prev => ({
        ...prev,
        balances: prev.balances.map(balance => {
          if (balance.currency === request.fromCurrency) {
            return {
              ...balance,
              amount: balance.amount - request.amount,
              lastUpdated: new Date()
            }
          }
          if (balance.currency === request.toCurrency) {
            return {
              ...balance,
              amount: balance.amount + conversion.convertedAmount,
              lastUpdated: new Date()
            }
          }
          return balance
        })
      }))

      return conversion

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Currency conversion failed')
      return null
    } finally {
      setIsConverting(false)
    }
  }

  const getConversionPreview = async (
    fromCurrency: Currency,
    toCurrency: Currency,
    amount: number
  ): Promise<CurrencyConversion | null> => {
    try {
      const response = await fetch(
        `/api/wallet/convert/preview?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`
      )

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to get conversion preview')
      }

      return data.preview

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get conversion preview')
      return null
    }
  }

  const getConversionHistory = async (): Promise<CurrencyConversion[]> => {
    if (!userId) return []

    try {
      const response = await fetch(`/api/wallet/convert/history?userId=${userId}`)
      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch conversion history')
      }

      setConversionHistory(data.conversions)
      return data.conversions

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch conversion history')
      return []
    }
  }

  return {
    walletData,
    conversionHistory,
    isLoading,
    isConverting,
    error,
    refetch: fetchWalletData,
    updateCurrencyPreference,
    addTransaction,
    updateBalance,
    convertCurrency,
    getConversionPreview,
    getConversionHistory,
    refreshData: fetchWalletData
  }
}
