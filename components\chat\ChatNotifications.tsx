/**
 * ## Chat Notifications System
 * Handles real-time notifications, sound alerts, and browser notifications
 * Provides comprehensive notification management for chat system
 */

'use client'

import { useEffect, useState, useCallback } from 'react'
import { toast } from 'sonner'
import { Bell, BellOff, Volume2, VolumeX } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface ChatNotificationsProps {
  userId: string
  userType: 'customer' | 'admin'
  isEnabled?: boolean
  onToggle?: (enabled: boolean) => void
}

interface NotificationSettings {
  browserNotifications: boolean
  soundAlerts: boolean
  emailNotifications: boolean
  pushNotifications: boolean
}

export function ChatNotifications({ 
  userId, 
  userType, 
  isEnabled = true,
  onToggle 
}: ChatNotificationsProps) {
  const [settings, setSettings] = useState<NotificationSettings>({
    browserNotifications: true,
    soundAlerts: true,
    emailNotifications: false,
    pushNotifications: false
  })
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isVisible, setIsVisible] = useState(true)

  /**
   * ## Request Browser Notification Permission
   */
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      setPermission(permission)
      return permission === 'granted'
    }
    return false
  }, [])

  /**
   * ## Initialize Notification Settings
   */
  useEffect(() => {
    // Load saved settings from localStorage
    const savedSettings = localStorage.getItem(`chat_notifications_${userId}`)
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }

    // Check current notification permission
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }

    // Check if page is visible
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden)
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [userId])

  /**
   * ## Save Settings to localStorage
   */
  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    const updatedSettings = { ...settings, ...newSettings }
    setSettings(updatedSettings)
    localStorage.setItem(`chat_notifications_${userId}`, JSON.stringify(updatedSettings))
  }, [settings, userId])

  /**
   * ## Show Browser Notification
   */
  const showBrowserNotification = useCallback((title: string, message: string, icon?: string) => {
    if (!settings.browserNotifications || !isEnabled || isVisible) return
    if (permission !== 'granted') return

    try {
      const notification = new Notification(title, {
        body: message,
        icon: icon || '/favicon.ico',
        badge: '/favicon.ico',
        tag: `chat_${userId}`,
        requireInteraction: false,
        silent: !settings.soundAlerts
      })

      // Auto close after 5 seconds
      setTimeout(() => notification.close(), 5000)

      // Handle click to focus window
      notification.onclick = () => {
        window.focus()
        notification.close()
      }
    } catch (error) {
      console.error('Error showing notification:', error)
    }
  }, [settings.browserNotifications, settings.soundAlerts, isEnabled, isVisible, permission, userId])

  /**
   * ## Play Sound Alert
   */
  const playSoundAlert = useCallback((type: 'message' | 'mention' | 'system' = 'message') => {
    if (!settings.soundAlerts || !isEnabled) return

    try {
      // ## TODO: Add actual sound files to public/sounds/
      const soundFiles = {
        message: '/sounds/message.mp3',
        mention: '/sounds/mention.mp3',
        system: '/sounds/system.mp3'
      }

      const audio = new Audio(soundFiles[type])
      audio.volume = 0.5
      audio.play().catch(console.error)
    } catch (error) {
      console.error('Error playing sound:', error)
    }
  }, [settings.soundAlerts, isEnabled])

  /**
   * ## Show Toast Notification
   */
  const showToastNotification = useCallback((
    title: string, 
    message: string, 
    type: 'info' | 'success' | 'warning' | 'error' = 'info'
  ) => {
    if (!isEnabled) return

    const toastOptions = {
      description: message,
      duration: 4000,
    }

    switch (type) {
      case 'success':
        toast.success(title, toastOptions)
        break
      case 'warning':
        toast.warning(title, toastOptions)
        break
      case 'error':
        toast.error(title, toastOptions)
        break
      default:
        toast.info(title, toastOptions)
    }
  }, [isEnabled])

  /**
   * ## Handle New Message Notification
   */
  const handleNewMessage = useCallback((
    senderName: string,
    message: string,
    senderType: 'customer' | 'admin'
  ) => {
    if (!isEnabled) return

    const isFromOtherUser = senderType !== userType
    if (!isFromOtherUser) return

    const title = userType === 'admin' 
      ? `رسالة جديدة من ${senderName}`
      : 'رسالة جديدة من الدعم الفني'

    // Show browser notification if page is not visible
    if (!isVisible) {
      showBrowserNotification(title, message)
    }

    // Show toast notification
    showToastNotification(title, message, 'info')

    // Play sound alert
    playSoundAlert('message')
  }, [isEnabled, userType, isVisible, showBrowserNotification, showToastNotification, playSoundAlert])

  /**
   * ## Notification Settings Panel
   */
  const NotificationSettingsPanel = () => (
    <Card className="bg-slate-800/50 border-slate-700/50">
      <CardHeader>
        <CardTitle className="text-white text-lg flex items-center gap-2">
          <Bell className="h-5 w-5" />
          إعدادات الإشعارات
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Browser Notifications */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-white font-medium">إشعارات المتصفح</label>
            <p className="text-sm text-slate-400">عرض إشعارات عند وصول رسائل جديدة</p>
          </div>
          <div className="flex items-center gap-2">
            {permission !== 'granted' && (
              <Button
                size="sm"
                variant="outline"
                onClick={requestNotificationPermission}
                className="text-xs"
              >
                تفعيل
              </Button>
            )}
            <Switch
              checked={settings.browserNotifications && permission === 'granted'}
              onCheckedChange={(checked) => {
                if (checked && permission !== 'granted') {
                  requestNotificationPermission().then((granted) => {
                    if (granted) {
                      updateSettings({ browserNotifications: true })
                    }
                  })
                } else {
                  updateSettings({ browserNotifications: checked })
                }
              }}
              disabled={permission === 'denied'}
            />
          </div>
        </div>

        {/* Sound Alerts */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-white font-medium">التنبيهات الصوتية</label>
            <p className="text-sm text-slate-400">تشغيل صوت عند وصول رسائل جديدة</p>
          </div>
          <Switch
            checked={settings.soundAlerts}
            onCheckedChange={(checked) => updateSettings({ soundAlerts: checked })}
          />
        </div>

        {/* Email Notifications */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-white font-medium">إشعارات البريد الإلكتروني</label>
            <p className="text-sm text-slate-400">إرسال ملخص يومي للرسائل</p>
          </div>
          <Switch
            checked={settings.emailNotifications}
            onCheckedChange={(checked) => updateSettings({ emailNotifications: checked })}
          />
        </div>

        {/* Master Toggle */}
        <div className="pt-4 border-t border-slate-700/50">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-white font-medium">تفعيل جميع الإشعارات</label>
              <p className="text-sm text-slate-400">إيقاف/تشغيل جميع أنواع الإشعارات</p>
            </div>
            <Switch
              checked={isEnabled}
              onCheckedChange={onToggle}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  /**
   * ## Quick Toggle Button
   */
  const QuickToggleButton = () => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onToggle?.(!isEnabled)}
      className="text-slate-400 hover:text-white"
    >
      {isEnabled ? (
        <Bell className="h-4 w-4" />
      ) : (
        <BellOff className="h-4 w-4" />
      )}
    </Button>
  )

  // Expose notification methods for external use
  useEffect(() => {
    // ## TODO: Setup Supabase real-time listener for new messages
    // const subscription = supabase
    //   .channel('chat_notifications')
    //   .on('postgres_changes', {
    //     event: 'INSERT',
    //     schema: 'public',
    //     table: 'chats',
    //     filter: userType === 'customer' 
    //       ? `user_id=eq.${userId}`
    //       : undefined
    //   }, (payload) => {
    //     const message = payload.new as ChatMessage
    //     handleNewMessage(
    //       message.senderType === 'customer' ? 'العميل' : 'الدعم الفني',
    //       message.message,
    //       message.senderType
    //     )
    //   })
    //   .subscribe()

    // return () => subscription.unsubscribe()
  }, [userId, userType, handleNewMessage])

  return {
    NotificationSettingsPanel,
    QuickToggleButton,
    showBrowserNotification,
    showToastNotification,
    playSoundAlert,
    handleNewMessage,
    settings,
    isEnabled
  }
}

/**
 * ## Notification Permission Status Component
 */
export function NotificationPermissionStatus() {
  const [permission, setPermission] = useState<NotificationPermission>('default')

  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  const getStatusColor = () => {
    switch (permission) {
      case 'granted': return 'text-green-400'
      case 'denied': return 'text-red-400'
      default: return 'text-yellow-400'
    }
  }

  const getStatusText = () => {
    switch (permission) {
      case 'granted': return 'مفعلة'
      case 'denied': return 'مرفوضة'
      default: return 'في الانتظار'
    }
  }

  return (
    <div className={`text-sm ${getStatusColor()}`}>
      إشعارات المتصفح: {getStatusText()}
    </div>
  )
}
