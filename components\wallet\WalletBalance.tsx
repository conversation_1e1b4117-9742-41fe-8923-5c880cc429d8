"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { CurrencySelector } from "@/components/wallet/CurrencySelector"
import { WalletData, Currency } from "@/lib/types"
import { getBalanceForCurrency } from "@/lib/data/mockWalletData"
import { formatCurrency } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"
import { Wallet, Plus, TrendingUp } from "lucide-react"
import { useCurrency } from "@/contexts/CurrencyContext"

interface WalletBalanceProps {
  walletData: WalletData
  selectedCurrency: Currency
  onCurrencyChange: (currency: Currency) => void
  onAddBalance: () => void
  isLoading: boolean
}

export function WalletBalance({
  walletData,
  selectedCurrency,
  onCurrencyChange,
  onAddBalance,
  isLoading
}: WalletBalanceProps) {
  // Use global currency context for available currencies
  const { availableCurrencies } = useCurrency()

  // ## Get current balance for selected currency - will be replaced with Supabase query
  const currentBalance = getBalanceForCurrency(walletData, selectedCurrency)

  // ## Get total purchases in selected currency - will be calculated from Supabase
  const totalPurchases = walletData.totalPurchases

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Main Balance Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-xl text-white">
            <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
              <Wallet className="h-6 w-6 text-slate-900" />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Currency Selector */}
          <div className="space-y-3">
            <p className="text-slate-300 text-sm font-medium">اختر العملة:</p>
            <CurrencySelector
              selectedCurrency={selectedCurrency}
              onCurrencyChange={onCurrencyChange}
              availableCurrencies={availableCurrencies}
              disabled={isLoading}
            />
          </div>

          {/* Current Balance Display */}
          <div className="space-y-3">
            <p className="text-slate-300 text-sm font-medium">رصيدك الحالي:</p>
            {isLoading ? (
              <Skeleton className="h-16 w-full max-w-xs" />
            ) : (
              <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
                <div className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent leading-tight">
                  {/* ## Display formatted balance - data from Supabase wallet table */}
                  {formatCurrency(currentBalance, selectedCurrency)}
                </div>
              </div>
            )}
          </div>

          {/* Add Balance Button */}
          <div className="pt-2">
            <Button
              onClick={onAddBalance}
              disabled={isLoading}
              className={cn(
                "w-full h-14 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600",
                "text-slate-900 font-bold rounded-xl transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl",
                "relative overflow-hidden border-0"
              )}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
              <div className="flex items-center justify-center gap-3 relative z-10">
                <Plus className="h-6 w-6" />
                <span className="text-xl font-bold">إضافة رصيد</span>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Card */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-xl text-white">
            <div className="p-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            إحصائيات المحفظة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Total Purchases */}
          <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
            <p className="text-slate-300 text-sm font-medium mb-3">إجمالي المشتريات:</p>
            {isLoading ? (
              <Skeleton className="h-8 w-32" />
            ) : (
              <div className="text-xl lg:text-2xl font-bold text-green-400">
                {/* ## Display total purchases - calculated from Supabase transactions */}
                {formatCurrency(totalPurchases, selectedCurrency)}
              </div>
            )}
          </div>

          {/* Current Balance (Redundant Display) */}
          <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
            <p className="text-slate-300 text-sm font-medium mb-3">الرصيد الحالي:</p>
            {isLoading ? (
              <Skeleton className="h-8 w-32" />
            ) : (
              <div className="text-xl lg:text-2xl font-bold text-blue-400">
                {/* ## Redundant balance display as requested */}
                {formatCurrency(currentBalance, selectedCurrency)}
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-slate-700/30 rounded-xl p-4 text-center border border-slate-600/30">
              <div className="text-xl lg:text-2xl font-bold text-yellow-400 mb-2">
                {walletData.transactions.filter(t => t.type === 'deposit' && t.currency === selectedCurrency).length}
              </div>
              <div className="text-xs text-slate-400 font-medium">إيداعات</div>
            </div>
            <div className="bg-slate-700/30 rounded-xl p-4 text-center border border-slate-600/30">
              <div className="text-xl lg:text-2xl font-bold text-orange-400 mb-2">
                {walletData.transactions.filter(t => t.type === 'purchase' && t.currency === selectedCurrency).length}
              </div>
              <div className="text-xs text-slate-400 font-medium">مشتريات</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
