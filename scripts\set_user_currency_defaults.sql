-- =====================================================
-- User Currency Default Setup Script
-- Run this script to set USD as default for all users
-- =====================================================

-- =====================================================
-- 1. UPDATE EXISTING USER PREFERENCES TO USD
-- =====================================================

-- Update all existing user preferences to use USD as default
UPDATE user_preferences 
SET 
  preferred_currency_code = 'USD',
  display_currency_code = 'USD',
  updated_at = NOW()
WHERE preferred_currency_code != 'USD' OR display_currency_code != 'USD';

-- =====================================================
-- 2. CREATE USD WALLETS FOR ALL USERS
-- =====================================================

-- Create USD wallets for users who don't have one
INSERT INTO user_wallets (user_id, currency_code, balance)
SELECT 
  u.id as user_id,
  'USD' as currency_code,
  0.00 as balance
FROM auth.users u
WHERE NOT EXISTS (
  SELECT 1 FROM user_wallets uw 
  WHERE uw.user_id = u.id AND uw.currency_code = 'USD'
);

-- =====================================================
-- 3. UPDATE CLIENT SETTINGS TO USD PRIMARY
-- =====================================================

-- Update client settings to use USD as primary currency
UPDATE client_currency_settings 
SET 
  primary_currency_code = 'USD',
  enabled_currencies = ARRAY['USD', 'SDG', 'EGP'],
  updated_at = NOW()
WHERE primary_currency_code != 'USD';

-- =====================================================
-- 4. VERIFICATION QUERIES
-- =====================================================

-- Check user preferences
SELECT 
  'User Preferences' as table_name,
  COUNT(*) as total_users,
  COUNT(CASE WHEN preferred_currency_code = 'USD' THEN 1 END) as usd_preferred,
  COUNT(CASE WHEN display_currency_code = 'USD' THEN 1 END) as usd_display
FROM user_preferences;

-- Check USD wallets
SELECT 
  'USD Wallets' as table_name,
  COUNT(DISTINCT user_id) as users_with_usd_wallet,
  SUM(balance) as total_usd_balance
FROM user_wallets 
WHERE currency_code = 'USD';

-- Check client settings
SELECT 
  'Client Settings' as table_name,
  primary_currency_code,
  enabled_currencies,
  enable_multi_currency,
  enable_currency_conversion
FROM client_currency_settings;

-- =====================================================
-- 5. OPTIONAL: MIGRATE EXISTING BALANCES TO USD
-- =====================================================

-- UNCOMMENT THE FOLLOWING SECTION IF YOU WANT TO CONVERT
-- EXISTING SDG/EGP BALANCES TO USD EQUIVALENT

/*
-- Convert SDG balances to USD (using current exchange rate)
DO $$
DECLARE
  sdg_to_usd_rate DECIMAL(18,8);
  egp_to_usd_rate DECIMAL(18,8);
BEGIN
  -- Get current exchange rates
  SELECT rate INTO sdg_to_usd_rate 
  FROM exchange_rates 
  WHERE from_currency_code = 'SDG' AND to_currency_code = 'USD' AND is_active = true
  ORDER BY effective_date DESC LIMIT 1;
  
  SELECT rate INTO egp_to_usd_rate 
  FROM exchange_rates 
  WHERE from_currency_code = 'EGP' AND to_currency_code = 'USD' AND is_active = true
  ORDER BY effective_date DESC LIMIT 1;
  
  -- Convert SDG balances to USD
  IF sdg_to_usd_rate IS NOT NULL THEN
    UPDATE user_wallets 
    SET balance = balance + (
      SELECT COALESCE(sdg_wallet.balance * sdg_to_usd_rate, 0)
      FROM user_wallets sdg_wallet
      WHERE sdg_wallet.user_id = user_wallets.user_id 
        AND sdg_wallet.currency_code = 'SDG'
    )
    WHERE currency_code = 'USD'
      AND EXISTS (
        SELECT 1 FROM user_wallets sdg_wallet
        WHERE sdg_wallet.user_id = user_wallets.user_id 
          AND sdg_wallet.currency_code = 'SDG'
          AND sdg_wallet.balance > 0
      );
    
    -- Clear SDG balances after conversion
    UPDATE user_wallets SET balance = 0 WHERE currency_code = 'SDG';
    
    RAISE NOTICE 'Converted SDG balances to USD using rate: %', sdg_to_usd_rate;
  END IF;
  
  -- Convert EGP balances to USD
  IF egp_to_usd_rate IS NOT NULL THEN
    UPDATE user_wallets 
    SET balance = balance + (
      SELECT COALESCE(egp_wallet.balance * egp_to_usd_rate, 0)
      FROM user_wallets egp_wallet
      WHERE egp_wallet.user_id = user_wallets.user_id 
        AND egp_wallet.currency_code = 'EGP'
    )
    WHERE currency_code = 'USD'
      AND EXISTS (
        SELECT 1 FROM user_wallets egp_wallet
        WHERE egp_wallet.user_id = user_wallets.user_id 
          AND egp_wallet.currency_code = 'EGP'
          AND egp_wallet.balance > 0
      );
    
    -- Clear EGP balances after conversion
    UPDATE user_wallets SET balance = 0 WHERE currency_code = 'EGP';
    
    RAISE NOTICE 'Converted EGP balances to USD using rate: %', egp_to_usd_rate;
  END IF;
END $$;
*/

-- =====================================================
-- 6. CREATE SAMPLE USD TRANSACTIONS FOR TESTING
-- =====================================================

-- Add sample USD transactions for testing (optional)
-- UNCOMMENT IF YOU WANT SAMPLE DATA

/*
INSERT INTO wallet_transactions (
  user_id,
  wallet_id,
  transaction_type,
  amount,
  currency_code,
  description,
  status
)
SELECT 
  uw.user_id,
  uw.id as wallet_id,
  'deposit' as transaction_type,
  25.00 as amount,
  'USD' as currency_code,
  'Welcome bonus - USD wallet' as description,
  'completed' as status
FROM user_wallets uw
WHERE uw.currency_code = 'USD'
  AND NOT EXISTS (
    SELECT 1 FROM wallet_transactions wt
    WHERE wt.wallet_id = uw.id AND wt.description LIKE '%Welcome bonus%'
  )
LIMIT 10; -- Limit to first 10 users for testing
*/

-- =====================================================
-- 7. FINAL SUMMARY
-- =====================================================

DO $$
DECLARE
  total_users INTEGER;
  usd_wallets INTEGER;
  usd_preferences INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_users FROM auth.users;
  SELECT COUNT(DISTINCT user_id) INTO usd_wallets FROM user_wallets WHERE currency_code = 'USD';
  SELECT COUNT(*) INTO usd_preferences FROM user_preferences WHERE preferred_currency_code = 'USD';
  
  RAISE NOTICE '=== USD DEFAULT SETUP COMPLETED ===';
  RAISE NOTICE 'Total users: %', total_users;
  RAISE NOTICE 'Users with USD wallets: %', usd_wallets;
  RAISE NOTICE 'Users with USD preference: %', usd_preferences;
  RAISE NOTICE '';
  RAISE NOTICE 'All users now have:';
  RAISE NOTICE '✓ USD as default preferred currency';
  RAISE NOTICE '✓ USD as default display currency';
  RAISE NOTICE '✓ USD wallet created (if not existed)';
  RAISE NOTICE '✓ Client settings updated to USD primary';
  RAISE NOTICE '';
  RAISE NOTICE 'Users can change their currency preference anytime in the wallet settings.';
END $$;
