"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Layout, 
  Grid, 
  Columns, 
  Smartphone, 
  Monitor,
  Palette,
  Settings
} from "lucide-react"
import { DynamicField, ProductLayout, LayoutSection } from "@/lib/types"

interface LayoutDesignerProps {
  layout: ProductLayout
  fields: DynamicField[]
  onChange: (layout: ProductLayout) => void
}

export function LayoutDesigner({ layout, fields, onChange }: LayoutDesignerProps) {
  const [previewMode, setPreviewMode] = useState<"desktop" | "mobile">("desktop")

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-white flex items-center gap-2">
              <Layout className="h-5 w-5" />
              مصمم التخطيط
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={previewMode === "desktop" ? "default" : "outline"}
                size="sm"
                onClick={() => setPreviewMode("desktop")}
                className="border-slate-600"
              >
                <Monitor className="h-4 w-4 ml-2" />
                سطح المكتب
              </Button>
              <Button
                variant={previewMode === "mobile" ? "default" : "outline"}
                size="sm"
                onClick={() => setPreviewMode("mobile")}
                className="border-slate-600"
              >
                <Smartphone className="h-4 w-4 ml-2" />
                الجوال
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Layout Designer Content */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-8 text-center">
          <Layout className="h-16 w-16 text-slate-400 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-white mb-2">مصمم التخطيط</h3>
          <p className="text-slate-300 mb-4">
            سيتم تطوير مصمم التخطيط المرئي قريباً. حالياً يمكنك إدارة الحقول من تبويب "الحقول".
          </p>
          <Badge variant="secondary" className="text-xs">
            قيد التطوير
          </Badge>
        </CardContent>
      </Card>

      {/* Theme Settings */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Palette className="h-5 w-5" />
            إعدادات المظهر
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm text-slate-300">اللون الأساسي</label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-slate-600"
                  style={{ backgroundColor: layout.theme.primaryColor }}
                />
                <span className="text-slate-400 text-sm">{layout.theme.primaryColor}</span>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-slate-300">اللون الثانوي</label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-slate-600"
                  style={{ backgroundColor: layout.theme.secondaryColor }}
                />
                <span className="text-slate-400 text-sm">{layout.theme.secondaryColor}</span>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-slate-300">لون الخلفية</label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-slate-600"
                  style={{ backgroundColor: layout.theme.backgroundColor }}
                />
                <span className="text-slate-400 text-sm">{layout.theme.backgroundColor}</span>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm text-slate-300">لون النص</label>
              <div className="flex items-center gap-2">
                <div 
                  className="w-8 h-8 rounded border border-slate-600"
                  style={{ backgroundColor: layout.theme.textColor }}
                />
                <span className="text-slate-400 text-sm">{layout.theme.textColor}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
