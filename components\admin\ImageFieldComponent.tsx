"use client"

import React, { useState } from "react"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { ImageUpload } from "@/components/profile/ImageUpload"
import { 
  Image as ImageIcon, 
  X, 
  Eye,
  Download
} from "lucide-react"
import { ImageField } from "@/lib/types"

interface ImageFieldComponentProps {
  field: ImageField
  value?: string | string[]
  onChange?: (value: string | string[]) => void
  disabled?: boolean
  preview?: boolean
}

export function ImageFieldComponent({ 
  field, 
  value, 
  onChange, 
  disabled = false,
  preview = false 
}: ImageFieldComponentProps) {
  const [uploadedImages, setUploadedImages] = useState<string[]>(
    Array.isArray(value) ? value : value ? [value] : []
  )

  const handleImageUpload = (imageUrl: string) => {
    if (field.multiple) {
      const newImages = [...uploadedImages, imageUrl]
      setUploadedImages(newImages)
      onChange?.(newImages)
    } else {
      setUploadedImages([imageUrl])
      onChange?.(imageUrl)
    }
  }

  const handleImageRemove = (imageUrl: string) => {
    const newImages = uploadedImages.filter(img => img !== imageUrl)
    setUploadedImages(newImages)
    
    if (field.multiple) {
      onChange?.(newImages)
    } else {
      onChange?.(newImages[0] || "")
    }
  }

  const handleImageView = (imageUrl: string) => {
    window.open(imageUrl, '_blank')
  }

  if (preview) {
    return (
      <div className="space-y-2">
        <Label className="text-slate-300">
          {field.label}
          {field.required && <span className="text-red-400 mr-1">*</span>}
        </Label>
        
        {uploadedImages.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {uploadedImages.map((imageUrl, index) => (
              <div key={index} className="relative group">
                <img
                  src={imageUrl}
                  alt={`${field.label} ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg border border-slate-600"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleImageView(imageUrl)}
                    className="border-white/20 text-white hover:bg-white/20"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  {!disabled && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleImageRemove(imageUrl)}
                      className="border-red-400/20 text-red-400 hover:bg-red-400/20"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="border-2 border-dashed border-slate-600 rounded-lg p-8 text-center">
            <ImageIcon className="h-12 w-12 text-slate-400 mx-auto mb-2" />
            <p className="text-slate-400">لم يتم رفع صور بعد</p>
          </div>
        )}
        
        {field.description && (
          <p className="text-sm text-slate-400">{field.description}</p>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Label className="text-slate-300">
        {field.label}
        {field.required && <span className="text-red-400 mr-1">*</span>}
      </Label>
      
      {/* Uploaded Images Display */}
      {uploadedImages.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-4">
          {uploadedImages.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <img
                src={imageUrl}
                alt={`${field.label} ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg border border-slate-600"
              />
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleImageView(imageUrl)}
                  className="border-white/20 text-white hover:bg-white/20"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleImageRemove(imageUrl)}
                  className="border-red-400/20 text-red-400 hover:bg-red-400/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Upload Component */}
      {(field.multiple || uploadedImages.length === 0) && !disabled && (
        <ImageUpload
          onImageUpload={handleImageUpload}
          maxSizeInMB={field.maxSize || 5}
          acceptedFormats={field.allowedTypes || ['image/jpeg', 'image/png', 'image/webp']}
        />
      )}
      
      {/* Upload Limits Info */}
      <div className="text-xs text-slate-500 space-y-1">
        <p>
          الأنواع المدعومة: {field.allowedTypes?.join(', ') || 'JPG, PNG, WebP'}
        </p>
        <p>
          الحد الأقصى للحجم: {field.maxSize || 5} ميجابايت
        </p>
        {field.multiple && (
          <p>يمكن رفع عدة صور</p>
        )}
      </div>
      
      {field.description && (
        <p className="text-sm text-slate-400">{field.description}</p>
      )}
    </div>
  )
}
