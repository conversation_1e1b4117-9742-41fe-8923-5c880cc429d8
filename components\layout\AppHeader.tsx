"use client"

import { Grid3X3 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import Image from "next/image"
import { useRouter } from "next/navigation"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  const router = useRouter()

  const handleProfileClick = () => {
    router.push("/profile")
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between p-4 lg:p-6 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      {/* Profile Picture - Left */}
      <div className="flex items-center">
        <Avatar
          className="h-10 w-10 border-2 border-yellow-400/20 cursor-pointer hover:border-yellow-400/40 transition-all duration-300 hover:scale-110"
          onClick={handleProfileClick}
        >
          <AvatarImage src="" alt="Profile" />
          <AvatarFallback className="bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-sm font-bold">
            ر
          </AvatarFallback>
        </Avatar>
      </div>

      {/* Logo - Center */}
      <div className="flex items-center justify-center">
        <Image
          src="/logo-without-background.png"
          alt="رايه شوب"
          width={200}
          height={80}
          className="h-12 lg:h-16 w-auto object-contain"
          priority
        />
      </div>

      {/* Menu Widget - Right */}
      <Button
        variant="ghost"
        size="icon"
        className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
        onClick={onMenuOpen}
      >
        <Grid3X3 className="h-6 w-6" />
      </Button>
    </header>
  )
}
