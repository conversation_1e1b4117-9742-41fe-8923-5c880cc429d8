"use client"

import React, { create<PERSON>ontext, use<PERSON>ontext, useReducer, ReactNode } from "react"
import { CheckoutData, CheckoutUserDetails, BankAccount, Currency } from "@/lib/types"

// ## Initial checkout state
const initialCheckoutState: CheckoutData = {
  step: 1,
  amount: 0,
  currency: "SDG",
  userDetails: null,
  selectedBank: null,
  referenceNumber: "",
  receiptFile: null,
  receiptPreview: null
}

// ## Checkout action types
type CheckoutAction =
  | { type: "SET_STEP"; payload: 1 | 2 | 3 }
  | { type: "SET_AMOUNT"; payload: number }
  | { type: "SET_CURRENCY"; payload: Currency }
  | { type: "SET_USER_DETAILS"; payload: CheckoutUserDetails }
  | { type: "SET_SELECTED_BANK"; payload: BankAccount }
  | { type: "SET_REFERENCE_NUMBER"; payload: string }
  | { type: "SET_RECEIPT_FILE"; payload: { file: File | null; preview: string | null } }
  | { type: "RESET_CHECKOUT" }
  | { type: "GO_TO_NEXT_STEP" }
  | { type: "GO_TO_PREVIOUS_STEP" }

// ## Checkout reducer
function checkoutReducer(state: CheckoutData, action: CheckoutAction): CheckoutData {
  switch (action.type) {
    case "SET_STEP":
      return { ...state, step: action.payload }
    
    case "SET_AMOUNT":
      return { ...state, amount: action.payload }
    
    case "SET_CURRENCY":
      return { ...state, currency: action.payload }
    
    case "SET_USER_DETAILS":
      return { ...state, userDetails: action.payload }
    
    case "SET_SELECTED_BANK":
      return { ...state, selectedBank: action.payload }
    
    case "SET_REFERENCE_NUMBER":
      return { ...state, referenceNumber: action.payload }
    
    case "SET_RECEIPT_FILE":
      return { 
        ...state, 
        receiptFile: action.payload.file,
        receiptPreview: action.payload.preview
      }
    
    case "GO_TO_NEXT_STEP":
      return { 
        ...state, 
        step: Math.min(3, state.step + 1) as 1 | 2 | 3
      }
    
    case "GO_TO_PREVIOUS_STEP":
      return { 
        ...state, 
        step: Math.max(1, state.step - 1) as 1 | 2 | 3
      }
    
    case "RESET_CHECKOUT":
      return initialCheckoutState
    
    default:
      return state
  }
}

// ## Context interface
interface CheckoutContextType {
  state: CheckoutData
  dispatch: React.Dispatch<CheckoutAction>
  // Helper functions
  setStep: (step: 1 | 2 | 3) => void
  setAmount: (amount: number) => void
  setCurrency: (currency: Currency) => void
  setUserDetails: (details: CheckoutUserDetails) => void
  setSelectedBank: (bank: BankAccount) => void
  setReferenceNumber: (reference: string) => void
  setReceiptFile: (file: File | null, preview: string | null) => void
  goToNextStep: () => void
  goToPreviousStep: () => void
  resetCheckout: () => void
  // Validation functions
  canProceedToStep2: () => boolean
  canProceedToStep3: () => boolean
  canSubmitOrder: () => boolean
}

// ## Create context
const CheckoutContext = createContext<CheckoutContextType | undefined>(undefined)

// ## Context provider component
interface CheckoutProviderProps {
  children: ReactNode
}

export function CheckoutProvider({ children }: CheckoutProviderProps) {
  const [state, dispatch] = useReducer(checkoutReducer, initialCheckoutState)

  // ## Helper functions
  const setStep = (step: 1 | 2 | 3) => {
    dispatch({ type: "SET_STEP", payload: step })
  }

  const setAmount = (amount: number) => {
    dispatch({ type: "SET_AMOUNT", payload: amount })
  }

  const setCurrency = (currency: Currency) => {
    dispatch({ type: "SET_CURRENCY", payload: currency })
  }

  const setUserDetails = (details: CheckoutUserDetails) => {
    dispatch({ type: "SET_USER_DETAILS", payload: details })
  }

  const setSelectedBank = (bank: BankAccount) => {
    dispatch({ type: "SET_SELECTED_BANK", payload: bank })
  }

  const setReferenceNumber = (reference: string) => {
    dispatch({ type: "SET_REFERENCE_NUMBER", payload: reference })
  }

  const setReceiptFile = (file: File | null, preview: string | null) => {
    dispatch({ type: "SET_RECEIPT_FILE", payload: { file, preview } })
  }

  const goToNextStep = () => {
    dispatch({ type: "GO_TO_NEXT_STEP" })
  }

  const goToPreviousStep = () => {
    dispatch({ type: "GO_TO_PREVIOUS_STEP" })
  }

  const resetCheckout = () => {
    dispatch({ type: "RESET_CHECKOUT" })
  }

  // ## Validation functions
  const canProceedToStep2 = (): boolean => {
    return state.amount > 0
  }

  const canProceedToStep3 = (): boolean => {
    return (
      state.amount > 0 &&
      state.userDetails !== null &&
      state.userDetails.firstName.trim() !== "" &&
      state.userDetails.lastName.trim() !== "" &&
      state.userDetails.phone.trim() !== "" &&
      state.userDetails.email.trim() !== ""
    )
  }

  const canSubmitOrder = (): boolean => {
    return (
      canProceedToStep3() &&
      state.selectedBank !== null &&
      state.referenceNumber.trim() !== "" &&
      state.receiptFile !== null
    )
  }

  const contextValue: CheckoutContextType = {
    state,
    dispatch,
    setStep,
    setAmount,
    setCurrency,
    setUserDetails,
    setSelectedBank,
    setReferenceNumber,
    setReceiptFile,
    goToNextStep,
    goToPreviousStep,
    resetCheckout,
    canProceedToStep2,
    canProceedToStep3,
    canSubmitOrder
  }

  return (
    <CheckoutContext.Provider value={contextValue}>
      {children}
    </CheckoutContext.Provider>
  )
}

// ## Custom hook to use checkout context
export function useCheckout() {
  const context = useContext(CheckoutContext)
  if (context === undefined) {
    throw new Error("useCheckout must be used within a CheckoutProvider")
  }
  return context
}
