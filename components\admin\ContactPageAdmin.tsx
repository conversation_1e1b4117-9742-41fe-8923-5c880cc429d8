"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Phone,
  Mail,
  MessageCircle,
  Clock,
  MapPin,
  Edit3,
  Plus,
  Trash2,
  Save,
  X,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  Copy,
  Download,
  Upload,
  Settings,
  Users,
  HelpCircle,
  Shield,
  Zap,
  Target,
  Heart,
  Award,
  ChevronRight,
  FileText,
  BarChart3
} from "lucide-react"
import {
  ContactPageContent,
  ContactInfo,
  ContactFormField,
  AboutUsSection,
  CompanyStats,
  FAQ,
  TrustIndicator,
  ContactPageHeader,
  ContactFormSubmission
} from "@/lib/types"

// Mock data - will be replaced with Supabase queries
const mockContactData: ContactPageContent = {
  id: "contact_1",
  header: {
    id: "header_1",
    title: "اتصل بنا",
    description: "نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  contactInfo: {
    id: "info_1",
    whatsapp: "+*********** 789",
    email: "<EMAIL>",
    workingHours: "8:00 ص - 12:00 م (يومياً)",
    location: "الخرطوم، السودان",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  formFields: [
    {
      id: "field_1",
      name: "name",
      label: "الاسم",
      type: "text",
      required: true,
      placeholder: "اسمك الكامل",
      order: 1,
      isActive: true
    },
    {
      id: "field_2",
      name: "phone",
      label: "رقم الهاتف",
      type: "tel",
      required: true,
      placeholder: "+249xxxxxxxxx",
      order: 2,
      isActive: true
    },
    {
      id: "field_3",
      name: "email",
      label: "البريد الإلكتروني",
      type: "email",
      required: false,
      placeholder: "<EMAIL>",
      order: 3,
      isActive: true
    },
    {
      id: "field_4",
      name: "subject",
      label: "الموضوع",
      type: "text",
      required: true,
      placeholder: "موضوع رسالتك",
      order: 4,
      isActive: true
    },
    {
      id: "field_5",
      name: "message",
      label: "الرسالة",
      type: "textarea",
      required: true,
      placeholder: "اكتب رسالتك هنا...",
      order: 5,
      isActive: true
    }
  ],
  aboutUs: {
    id: "about_1",
    title: "من نحن",
    subtitle: "رايه شوب - رائدون في عالم الألعاب الرقمية",
    vision: {
      title: "رؤيتنا",
      description: "أن نكون المتجر الرقمي الأول في السودان والمنطقة لشحن الألعاب والخدمات الرقمية، نقدم أفضل تجربة للاعبين مع أسرع خدمة وأفضل الأسعار.",
      icon: "Target"
    },
    mission: {
      title: "مهمتنا",
      description: "تمكين اللاعبين من الاستمتاع بألعابهم المفضلة من خلال توفير خدمات شحن سريعة وآمنة وموثوقة، مع دعم فني متميز على مدار الساعة.",
      icon: "Heart"
    },
    values: {
      title: "قيمنا",
      items: [
        "الثقة والشفافية في جميع التعاملات",
        "السرعة والكفاءة في تقديم الخدمات",
        "الابتكار المستمر لتحسين التجربة",
        "دعم المجتمع المحلي للألعاب"
      ],
      icon: "Award"
    },
    team: {
      title: "فريقنا",
      description: "فريق من المتخصصين والمتحمسين للألعاب، نعمل بجد لضمان حصولك على أفضل تجربة شحن ممكنة.",
      icon: "Users"
    },
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  stats: {
    id: "stats_1",
    customers: {
      value: "+10K",
      label: "عميل راضي",
      color: "text-yellow-400"
    },
    successRate: {
      value: "99%",
      label: "نجاح العمليات",
      color: "text-green-400"
    },
    averageTime: {
      value: "<1 دقيقة",
      label: "متوسط الشحن",
      color: "text-blue-400"
    },
    support: {
      value: "24/7",
      label: "دعم فني",
      color: "text-purple-400"
    },
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  faqs: [
    {
      id: "faq_1",
      question: "كم يستغرق وقت الشحن؟",
      answer: "معظم عمليات الشحن تتم خلال أقل من دقيقة واحدة للمنتجات الفورية، و 5-15 دقيقة للمنتجات اليدوية.",
      order: 1,
      isActive: true,
      category: "general",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z"
    },
    {
      id: "faq_2",
      question: "ما هي طرق الدفع المتاحة؟",
      answer: "نقبل الدفع عبر البنك الأهلي، بنك فيصل الإسلامي، بنك الخرطوم، والمحافظ الإلكترونية مثل بنكك.",
      order: 2,
      isActive: true,
      category: "payment",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z"
    }
  ],
  trustIndicators: [
    {
      id: "trust_1",
      title: "أمان مضمون",
      description: "جميع المعاملات محمية بأعلى معايير الأمان",
      icon: "Shield",
      color: "text-green-400",
      order: 1,
      isActive: true,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z"
    },
    {
      id: "trust_2",
      title: "شحن فوري",
      description: "معظم عمليات الشحن تتم خلال أقل من دقيقة",
      icon: "Zap",
      color: "text-yellow-400",
      order: 2,
      isActive: true,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z"
    },
    {
      id: "trust_3",
      title: "دعم متميز",
      description: "فريق دعم متخصص متاح على مدار الساعة",
      icon: "Users",
      color: "text-blue-400",
      order: 3,
      isActive: true,
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z"
    }
  ],
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z"
}

export function ContactPageAdmin() {
  const [contactData, setContactData] = useState<ContactPageContent>(mockContactData)
  const [activeTab, setActiveTab] = useState("overview")
  const [isLoading, setIsLoading] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editType, setEditType] = useState<'field' | 'faq' | 'trust' | 'new'>('new')
  const [formData, setFormData] = useState<Record<string, any>>({})

  // Handle save operation
  const handleSave = async (section: string, data: any) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update local state
      setContactData(prev => ({
        ...prev,
        [section]: data,
        updatedAt: new Date().toISOString()
      }))
      
      setIsDialogOpen(false)
      setEditingItem(null)
    } catch (error) {
      console.error("Save error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle edit form field
  const handleEditField = (field: ContactFormField) => {
    setEditingItem(field)
    setEditType('field')
    setFormData({
      name: field.name,
      label: field.label,
      type: field.type,
      required: field.required,
      placeholder: field.placeholder
    })
    setIsDialogOpen(true)
  }

  // Handle edit FAQ
  const handleEditFaq = (faq: FAQ) => {
    setEditingItem(faq)
    setEditType('faq')
    setFormData({
      question: faq.question,
      answer: faq.answer,
      category: faq.category || 'general',
      isActive: faq.isActive
    })
    setIsDialogOpen(true)
  }

  // Handle edit trust indicator
  const handleEditTrust = (trust: TrustIndicator) => {
    setEditingItem(trust)
    setEditType('trust')
    setFormData({
      title: trust.title,
      description: trust.description,
      icon: trust.icon,
      color: trust.color,
      isActive: trust.isActive
    })
    setIsDialogOpen(true)
  }

  // Handle delete item
  const handleDelete = async (type: 'field' | 'faq' | 'trust', id: string) => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (type === 'field') {
        setContactData(prev => ({
          ...prev,
          formFields: prev.formFields.filter(field => field.id !== id)
        }))
      } else if (type === 'faq') {
        setContactData(prev => ({
          ...prev,
          faqs: prev.faqs.filter(faq => faq.id !== id)
        }))
      } else if (type === 'trust') {
        setContactData(prev => ({
          ...prev,
          trustIndicators: prev.trustIndicators.filter(trust => trust.id !== id)
        }))
      }
    } catch (error) {
      console.error("Delete error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle save edited item
  const handleSaveItem = async () => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (editType === 'field' && editingItem) {
        const updatedFields = contactData.formFields.map(field => 
          field.id === editingItem.id 
            ? { ...field, ...formData, updatedAt: new Date().toISOString() }
            : field
        )
        setContactData(prev => ({ ...prev, formFields: updatedFields }))
      } else if (editType === 'faq' && editingItem) {
        const updatedFaqs = contactData.faqs.map(faq => 
          faq.id === editingItem.id 
            ? { ...faq, ...formData, updatedAt: new Date().toISOString() }
            : faq
        )
        setContactData(prev => ({ ...prev, faqs: updatedFaqs }))
      } else if (editType === 'trust' && editingItem) {
        const updatedTrust = contactData.trustIndicators.map(trust => 
          trust.id === editingItem.id 
            ? { ...trust, ...formData, updatedAt: new Date().toISOString() }
            : trust
        )
        setContactData(prev => ({ ...prev, trustIndicators: updatedTrust }))
      }
      
      setIsDialogOpen(false)
      setEditingItem(null)
      setFormData({})
    } catch (error) {
      console.error("Save item error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Overview Tab
  const OverviewTab = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">الرسائل</p>
                <p className="text-2xl font-bold text-white">247</p>
              </div>
              <MessageCircle className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">الأسئلة الشائعة</p>
                <p className="text-2xl font-bold text-white">{contactData.faqs.length}</p>
              </div>
              <HelpCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">حقول النموذج</p>
                <p className="text-2xl font-bold text-white">{contactData.formFields.length}</p>
              </div>
              <FileText className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">مؤشرات الثقة</p>
                <p className="text-2xl font-bold text-white">{contactData.trustIndicators.length}</p>
              </div>
              <Shield className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Edit3 className="h-5 w-5" />
              تحرير العنوان
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 text-sm mb-4">
              تحرير عنوان الصفحة والوصف
            </p>
            <Button
              onClick={() => setActiveTab("header")}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            >
              تحرير العنوان
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Phone className="h-5 w-5" />
              معلومات الاتصال
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 text-sm mb-4">
              تحديث أرقام الهواتف والإيميلات
            </p>
            <Button
              onClick={() => setActiveTab("contact")}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
            >
              تحرير الاتصال
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              إدارة الأسئلة الشائعة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 text-sm mb-4">
              إضافة وتعديل الأسئلة الشائعة
            </p>
            <Button
              onClick={() => setActiveTab("faqs")}
              className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700"
            >
              إدارة الأسئلة
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )

  return (
    <div className="text-white p-4 lg:p-8 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
            إدارة صفحة الاتصال
          </h1>
          <p className="text-slate-400 text-lg">
            إدارة شاملة لجميع محتويات صفحة الاتصال والدعم الفني
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 bg-slate-800/50 border-slate-700/50 mb-6">
            <TabsTrigger value="overview" className="text-xs lg:text-sm">نظرة عامة</TabsTrigger>
            <TabsTrigger value="header" className="text-xs lg:text-sm">العنوان</TabsTrigger>
            <TabsTrigger value="contact" className="text-xs lg:text-sm">الاتصال</TabsTrigger>
            <TabsTrigger value="form" className="text-xs lg:text-sm">النموذج</TabsTrigger>
            <TabsTrigger value="about" className="text-xs lg:text-sm">من نحن</TabsTrigger>
            <TabsTrigger value="stats" className="text-xs lg:text-sm">الإحصائيات</TabsTrigger>
            <TabsTrigger value="faqs" className="text-xs lg:text-sm">الأسئلة</TabsTrigger>
            <TabsTrigger value="trust" className="text-xs lg:text-sm">الثقة</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <OverviewTab />
          </TabsContent>

          {/* Header Tab */}
          <TabsContent value="header">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Edit3 className="h-5 w-5" />
                  تحرير عنوان الصفحة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-slate-300">العنوان الرئيسي</Label>
                  <Input
                    value={contactData.header.title}
                    onChange={(e) => setContactData(prev => ({
                      ...prev,
                      header: { ...prev.header, title: e.target.value }
                    }))}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-slate-300">الوصف</Label>
                  <Textarea
                    value={contactData.header.description}
                    onChange={(e) => setContactData(prev => ({
                      ...prev,
                      header: { ...prev.header, description: e.target.value }
                    }))}
                    className="bg-slate-700/50 border-slate-600 text-white"
                    rows={3}
                  />
                </div>
                <Button
                  onClick={() => handleSave("header", contactData.header)}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
                >
                  {isLoading ? "جاري الحفظ..." : "حفظ التغييرات"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Contact Info Tab */}
          <TabsContent value="contact">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  معلومات الاتصال
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-slate-300 flex items-center gap-2">
                      <MessageCircle className="h-4 w-4" />
                      رقم الواتساب
                    </Label>
                    <Input
                      value={contactData.contactInfo.whatsapp}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, whatsapp: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="+*********** 789"
                    />
                  </div>
                  <div>
                    <Label className="text-slate-300 flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      البريد الإلكتروني
                    </Label>
                    <Input
                      value={contactData.contactInfo.email}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, email: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label className="text-slate-300 flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      ساعات العمل
                    </Label>
                    <Input
                      value={contactData.contactInfo.workingHours}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, workingHours: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="8:00 ص - 12:00 م (يومياً)"
                    />
                  </div>
                  <div>
                    <Label className="text-slate-300 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      الموقع
                    </Label>
                    <Input
                      value={contactData.contactInfo.location}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        contactInfo: { ...prev.contactInfo, location: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="الخرطوم، السودان"
                    />
                  </div>
                </div>
                <Button
                  onClick={() => handleSave("contactInfo", contactData.contactInfo)}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold"
                >
                  {isLoading ? "جاري الحفظ..." : "حفظ معلومات الاتصال"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Form Fields Tab */}
          <TabsContent value="form">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-bold text-white">إدارة حقول النموذج</h2>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      onClick={() => {
                        setEditingItem(null)
                        setEditType('field')
                        setFormData({})
                      }}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      إضافة حقل جديد
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
                    <DialogHeader>
                      <DialogTitle>
                        {editingItem ? 'تحرير' : 'إضافة'} 
                        {editType === 'field' ? ' الحقل' : editType === 'faq' ? ' السؤال' : ' مؤشر الثقة'}
                      </DialogTitle>
                      <DialogDescription className="text-slate-400">
                        {editingItem ? 'تحرير' : 'إضافة'} 
                        {editType === 'field' ? ' حقل النموذج' : editType === 'faq' ? ' السؤال الشائع' : ' مؤشر الثقة'}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      {editType === 'field' && (
                        <>
                          <div>
                            <Label className="text-slate-300">اسم الحقل</Label>
                            <Input
                              value={formData.name || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="مثال: name, phone, email"
                              className="bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">تسمية الحقل</Label>
                            <Input
                              value={formData.label || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
                              placeholder="مثال: الاسم، الهاتف، الإيميل"
                              className="bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">نوع الحقل</Label>
                            <Select value={formData.type || ''} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                                <SelectValue placeholder="اختر نوع الحقل" />
                              </SelectTrigger>
                              <SelectContent className="bg-slate-800 border-slate-700">
                                <SelectItem value="text">نص</SelectItem>
                                <SelectItem value="email">إيميل</SelectItem>
                                <SelectItem value="tel">هاتف</SelectItem>
                                <SelectItem value="textarea">نص طويل</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label className="text-slate-300">النص التوضيحي</Label>
                            <Input
                              value={formData.placeholder || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, placeholder: e.target.value }))}
                              placeholder="النص الذي يظهر داخل الحقل"
                              className="bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <Label className="text-slate-300">حقل مطلوب</Label>
                              <p className="text-sm text-slate-400">يجب على المستخدم ملء هذا الحقل</p>
                            </div>
                            <Switch
                              checked={formData.required || false}
                              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, required: checked }))}
                            />
                          </div>
                        </>
                      )}

                      {editType === 'faq' && (
                        <>
                          <div>
                            <Label className="text-slate-300">السؤال</Label>
                            <Input
                              value={formData.question || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
                              placeholder="اكتب السؤال هنا"
                              className="bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">الإجابة</Label>
                            <Textarea
                              value={formData.answer || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, answer: e.target.value }))}
                              placeholder="اكتب الإجابة هنا"
                              className="bg-slate-700/50 border-slate-600 text-white"
                              rows={4}
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">الفئة</Label>
                            <Select value={formData.category || 'general'} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                                <SelectValue placeholder="اختر الفئة" />
                              </SelectTrigger>
                              <SelectContent className="bg-slate-800 border-slate-700">
                                <SelectItem value="general">عام</SelectItem>
                                <SelectItem value="payment">الدفع</SelectItem>
                                <SelectItem value="delivery">التسليم</SelectItem>
                                <SelectItem value="support">الدعم</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <Label className="text-slate-300">نشط</Label>
                              <p className="text-sm text-slate-400">إظهار هذا السؤال للمستخدمين</p>
                            </div>
                            <Switch
                              checked={formData.isActive || false}
                              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                            />
                          </div>
                        </>
                      )}

                      {editType === 'trust' && (
                        <>
                          <div>
                            <Label className="text-slate-300">العنوان</Label>
                            <Input
                              value={formData.title || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                              placeholder="عنوان مؤشر الثقة"
                              className="bg-slate-700/50 border-slate-600 text-white"
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">الوصف</Label>
                            <Textarea
                              value={formData.description || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                              placeholder="وصف مؤشر الثقة"
                              className="bg-slate-700/50 border-slate-600 text-white"
                              rows={3}
                            />
                          </div>
                          <div>
                            <Label className="text-slate-300">اللون</Label>
                            <Select value={formData.color || ''} onValueChange={(value) => setFormData(prev => ({ ...prev, color: value }))}>
                              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                                <SelectValue placeholder="اختر اللون" />
                              </SelectTrigger>
                              <SelectContent className="bg-slate-800 border-slate-700">
                                <SelectItem value="text-green-400">أخضر</SelectItem>
                                <SelectItem value="text-blue-400">أزرق</SelectItem>
                                <SelectItem value="text-yellow-400">أصفر</SelectItem>
                                <SelectItem value="text-purple-400">بنفسجي</SelectItem>
                                <SelectItem value="text-red-400">أحمر</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <Label className="text-slate-300">نشط</Label>
                              <p className="text-sm text-slate-400">إظهار هذا المؤشر للمستخدمين</p>
                            </div>
                            <Switch
                              checked={formData.isActive || false}
                              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                            />
                          </div>
                        </>
                      )}

                      <Button 
                        onClick={handleSaveItem}
                        disabled={isLoading}
                        className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                      >
                        {isLoading ? "جاري الحفظ..." : (editingItem ? "حفظ التغييرات" : "إضافة")}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              <div className="grid gap-4">
                {contactData.formFields.map((field, index) => (
                  <Card key={field.id} className="bg-slate-800/50 border-slate-700/50">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-white">{field.label}</h3>
                            {field.required && (
                              <Badge variant="destructive" className="text-xs">مطلوب</Badge>
                            )}
                            <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                              {field.type}
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm">{field.placeholder}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newFields = [...contactData.formFields]
                              if (index > 0) {
                                [newFields[index], newFields[index - 1]] = [newFields[index - 1], newFields[index]]
                                setContactData(prev => ({ ...prev, formFields: newFields }))
                              }
                            }}
                            disabled={index === 0}
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newFields = [...contactData.formFields]
                              if (index < newFields.length - 1) {
                                [newFields[index], newFields[index + 1]] = [newFields[index + 1], newFields[index]]
                                setContactData(prev => ({ ...prev, formFields: newFields }))
                              }
                            }}
                            disabled={index === contactData.formFields.length - 1}
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleEditField(field)}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="bg-slate-800 border-slate-700">
                              <AlertDialogHeader>
                                <AlertDialogTitle className="text-white">حذف الحقل</AlertDialogTitle>
                                <AlertDialogDescription className="text-slate-400">
                                  هل أنت متأكد من حذف حقل "{field.label}"؟ لا يمكن التراجع عن هذا الإجراء.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600">
                                  إلغاء
                                </AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleDelete('field', field.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  حذف
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* About Us Tab */}
          <TabsContent value="about">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  تحرير قسم "من نحن"
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-slate-300">العنوان الرئيسي</Label>
                    <Input
                      value={contactData.aboutUs.title}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        aboutUs: { ...prev.aboutUs, title: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                  <div>
                    <Label className="text-slate-300">العنوان الفرعي</Label>
                    <Input
                      value={contactData.aboutUs.subtitle}
                      onChange={(e) => setContactData(prev => ({
                        ...prev,
                        aboutUs: { ...prev.aboutUs, subtitle: e.target.value }
                      }))}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                </div>

                <Separator className="bg-slate-700" />

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">الرؤية</h3>
                    <div>
                      <Label className="text-slate-300">عنوان الرؤية</Label>
                      <Input
                        value={contactData.aboutUs.vision.title}
                        onChange={(e) => setContactData(prev => ({
                          ...prev,
                          aboutUs: {
                            ...prev.aboutUs,
                            vision: { ...prev.aboutUs.vision, title: e.target.value }
                          }
                        }))}
                        className="bg-slate-700/50 border-slate-600 text-white"
                      />
                    </div>
                    <div>
                      <Label className="text-slate-300">وصف الرؤية</Label>
                      <Textarea
                        value={contactData.aboutUs.vision.description}
                        onChange={(e) => setContactData(prev => ({
                          ...prev,
                          aboutUs: {
                            ...prev.aboutUs,
                            vision: { ...prev.aboutUs.vision, description: e.target.value }
                          }
                        }))}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">المهمة</h3>
                    <div>
                      <Label className="text-slate-300">عنوان المهمة</Label>
                      <Input
                        value={contactData.aboutUs.mission.title}
                        onChange={(e) => setContactData(prev => ({
                          ...prev,
                          aboutUs: {
                            ...prev.aboutUs,
                            mission: { ...prev.aboutUs.mission, title: e.target.value }
                          }
                        }))}
                        className="bg-slate-700/50 border-slate-600 text-white"
                      />
                    </div>
                    <div>
                      <Label className="text-slate-300">وصف المهمة</Label>
                      <Textarea
                        value={contactData.aboutUs.mission.description}
                        onChange={(e) => setContactData(prev => ({
                          ...prev,
                          aboutUs: {
                            ...prev.aboutUs,
                            mission: { ...prev.aboutUs.mission, description: e.target.value }
                          }
                        }))}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        rows={3}
                      />
                    </div>
                  </div>
                </div>

                <Button
                  onClick={() => handleSave("aboutUs", contactData.aboutUs)}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold"
                >
                  {isLoading ? "جاري الحفظ..." : "حفظ معلومات الشركة"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Stats Tab */}
          <TabsContent value="stats">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  إحصائيات الشركة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">العملاء الراضين</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-slate-300">القيمة</Label>
                        <Input
                          value={contactData.stats.customers.value}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            stats: {
                              ...prev.stats,
                              customers: { ...prev.stats.customers, value: e.target.value }
                            }
                          }))}
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300">التسمية</Label>
                        <Input
                          value={contactData.stats.customers.label}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            stats: {
                              ...prev.stats,
                              customers: { ...prev.stats.customers, label: e.target.value }
                            }
                          }))}
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white">نسبة النجاح</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-slate-300">القيمة</Label>
                        <Input
                          value={contactData.stats.successRate.value}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            stats: {
                              ...prev.stats,
                              successRate: { ...prev.stats.successRate, value: e.target.value }
                            }
                          }))}
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300">التسمية</Label>
                        <Input
                          value={contactData.stats.successRate.label}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            stats: {
                              ...prev.stats,
                              successRate: { ...prev.stats.successRate, label: e.target.value }
                            }
                          }))}
                          className="bg-slate-700/50 border-slate-600 text-white"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={() => handleSave("stats", contactData.stats)}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-bold"
                >
                  {isLoading ? "جاري الحفظ..." : "حفظ الإحصائيات"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* FAQs Tab */}
          <TabsContent value="faqs">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-bold text-white">إدارة الأسئلة الشائعة</h2>
                <Button 
                  onClick={() => {
                    setEditingItem(null)
                    setEditType('faq')
                    setFormData({})
                    setIsDialogOpen(true)
                  }}
                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة سؤال جديد
                </Button>
              </div>

              <div className="grid gap-4">
                {contactData.faqs.map((faq, index) => (
                  <Card key={faq.id} className="bg-slate-800/50 border-slate-700/50">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-start gap-4">
                          <div className="flex-1">
                            <h3 className="font-semibold text-white mb-1">{faq.question}</h3>
                            <p className="text-slate-400 text-sm">{faq.answer}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="border-slate-600 text-slate-300">
                              {faq.category}
                            </Badge>
                            <div className="flex items-center justify-between min-w-[140px]">
                              <div className="space-y-1">
                                <Label className="text-slate-300 text-sm">نشط</Label>
                                <p className="text-xs text-slate-400">إظهار للمستخدمين</p>
                              </div>
                              <Switch 
                                checked={faq.isActive}
                                onCheckedChange={(checked) => {
                                  const updatedFaqs = contactData.faqs.map(f => 
                                    f.id === faq.id ? { ...f, isActive: checked } : f
                                  )
                                  setContactData(prev => ({ ...prev, faqs: updatedFaqs }))
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm">
                              <ArrowUp className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <ArrowDown className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleEditFaq(faq)}
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="bg-slate-800 border-slate-700">
                                <AlertDialogHeader>
                                  <AlertDialogTitle className="text-white">حذف السؤال</AlertDialogTitle>
                                  <AlertDialogDescription className="text-slate-400">
                                    هل أنت متأكد من حذف السؤال "{faq.question}"؟ لا يمكن التراجع عن هذا الإجراء.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600">
                                    إلغاء
                                  </AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleDelete('faq', faq.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    حذف
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Trust Indicators Tab */}
          <TabsContent value="trust">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-bold text-white">مؤشرات الثقة</h2>
                <Button 
                  onClick={() => {
                    setEditingItem(null)
                    setEditType('trust')
                    setFormData({})
                    setIsDialogOpen(true)
                  }}
                  className="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مؤشر جديد
                </Button>
              </div>

              <div className="grid gap-4">
                {contactData.trustIndicators.map((indicator, index) => (
                  <Card key={indicator.id} className="bg-slate-800/50 border-slate-700/50">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <div className={`p-2 rounded-lg bg-slate-700/30`}>
                              <Shield className={`h-5 w-5 ${indicator.color}`} />
                            </div>
                            <div>
                              <h3 className="font-semibold text-white">{indicator.title}</h3>
                              <p className="text-slate-400 text-sm">{indicator.description}</p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center justify-between min-w-[140px]">
                            <div className="space-y-1">
                              <Label className="text-slate-300 text-sm">نشط</Label>
                              <p className="text-xs text-slate-400">إظهار للمستخدمين</p>
                            </div>
                            <Switch 
                              checked={indicator.isActive}
                              onCheckedChange={(checked) => {
                                const updatedTrust = contactData.trustIndicators.map(t => 
                                  t.id === indicator.id ? { ...t, isActive: checked } : t
                                )
                                setContactData(prev => ({ ...prev, trustIndicators: updatedTrust }))
                              }}
                            />
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleEditTrust(indicator)}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="bg-slate-800 border-slate-700">
                              <AlertDialogHeader>
                                <AlertDialogTitle className="text-white">حذف مؤشر الثقة</AlertDialogTitle>
                                <AlertDialogDescription className="text-slate-400">
                                  هل أنت متأكد من حذف مؤشر "{indicator.title}"؟ لا يمكن التراجع عن هذا الإجراء.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600">
                                  إلغاء
                                </AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleDelete('trust', indicator.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  حذف
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 