"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { Transaction, Currency } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { 
  ShoppingBag, 
  Clock, 
  CheckCircle, 
  XCircle,
  Package,
  Gamepad2,
  Calendar
} from "lucide-react"

interface WalletOrdersProps {
  transactions: Transaction[]
  selectedCurrency: Currency
  isLoading: boolean
}

interface OrderItemProps {
  order: Transaction
}

function OrderItem({ order }: OrderItemProps) {
  const getStatusColor = () => {
    switch (order.status) {
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getStatusIcon = () => {
    switch (order.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "pending":
        return <Clock className="h-4 w-4" />
      case "failed":
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusText = () => {
    switch (order.status) {
      case "completed":
        return "مكتمل"
      case "pending":
        return "قيد المعالجة"
      case "failed":
        return "فشل"
      default:
        return "غير معروف"
    }
  }

  return (
    <Card className="bg-slate-700/30 border-slate-600/50 hover:bg-slate-700/50 transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg">
              <Gamepad2 className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">
                {order.description}
              </h4>
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <Calendar className="h-3 w-3" />
                {(() => {
                  // Consistent date formatting to prevent hydration mismatch
                  const dateObj = new Date(order.date)
                  const year = dateObj.getFullYear()
                  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
                  const day = String(dateObj.getDate()).padStart(2, '0')
                  const hours = String(dateObj.getHours()).padStart(2, '0')
                  const minutes = String(dateObj.getMinutes()).padStart(2, '0')

                  return `${day}/${month}/${year} ${hours}:${minutes}`
                })()}
              </div>
              {order.reference && (
                <div className="text-xs text-slate-500 mt-1">
                  رقم المرجع: {order.reference}
                </div>
              )}
            </div>
          </div>
          <div className="text-left">
            <div className="text-white font-bold text-sm mb-2">
              {formatCurrency(order.amount, order.currency)}
            </div>
            <Badge className={cn("text-xs border", getStatusColor())}>
              <div className="flex items-center gap-1">
                {getStatusIcon()}
                {getStatusText()}
              </div>
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function WalletOrders({
  transactions,
  selectedCurrency,
  isLoading
}: WalletOrdersProps) {
  const [activeFilter, setActiveFilter] = useState<"all" | "completed" | "pending" | "failed">("all")

  // ## Filter orders (purchase transactions) by status and currency
  const getFilteredOrders = () => {
    // Only show purchase transactions as orders
    let orders = transactions.filter(t => t.type === "purchase")
    
    // Filter by selected currency
    orders = orders.filter(t => t.currency === selectedCurrency)
    
    // Filter by status if not "all"
    if (activeFilter !== "all") {
      orders = orders.filter(t => t.status === activeFilter)
    }
    
    // Sort by date (newest first)
    return orders.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }

  const filteredOrders = getFilteredOrders()

  const getOrderStats = () => {
    const orders = transactions.filter(t => t.type === "purchase" && t.currency === selectedCurrency)
    return {
      total: orders.length,
      completed: orders.filter(t => t.status === "completed").length,
      pending: orders.filter(t => t.status === "pending").length,
      failed: orders.filter(t => t.status === "failed").length
    }
  }

  const stats = getOrderStats()

  const renderOrdersList = () => {
    if (isLoading) {
      return (
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="bg-slate-700/30 border-slate-600/50">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <Skeleton className="h-9 w-9 rounded-lg" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {filteredOrders.map((order) => (
          <OrderItem
            key={order.id}
            order={order}
          />
        ))}
      </div>
    )
  }

  return (
    <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl text-white">
          <div className="p-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl">
            <ShoppingBag className="h-6 w-6 text-white" />
          </div>
          طلباتي
        </CardTitle>
        
        {/* Order Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mt-4">
          <div className="bg-slate-700/30 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-white">{stats.total}</div>
            <div className="text-xs text-slate-400">إجمالي الطلبات</div>
          </div>
          <div className="bg-green-500/10 rounded-lg p-3 text-center border border-green-500/20">
            <div className="text-lg font-bold text-green-400">{stats.completed}</div>
            <div className="text-xs text-green-300">مكتملة</div>
          </div>
          <div className="bg-yellow-500/10 rounded-lg p-3 text-center border border-yellow-500/20">
            <div className="text-lg font-bold text-yellow-400">{stats.pending}</div>
            <div className="text-xs text-yellow-300">قيد المعالجة</div>
          </div>
          <div className="bg-red-500/10 rounded-lg p-3 text-center border border-red-500/20">
            <div className="text-lg font-bold text-red-400">{stats.failed}</div>
            <div className="text-xs text-red-300">فاشلة</div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2 mb-6">
          {[
            { id: "all", label: "جميع الطلبات", count: stats.total },
            { id: "completed", label: "مكتملة", count: stats.completed },
            { id: "pending", label: "قيد المعالجة", count: stats.pending },
            { id: "failed", label: "فاشلة", count: stats.failed }
          ].map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id as any)}
              className={cn(
                "flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 border-2",
                activeFilter === filter.id
                  ? "bg-gradient-to-r from-blue-400 to-purple-500 text-white border-transparent shadow-lg"
                  : "bg-slate-700/30 text-slate-300 border-slate-600/50 hover:border-blue-400/50 hover:text-blue-400"
              )}
            >
              {filter.label}
              <Badge className="bg-white/20 text-xs px-1.5 py-0.5">
                {filter.count}
              </Badge>
            </button>
          ))}
        </div>

        {/* Orders List */}
        {filteredOrders.length === 0 && !isLoading ? (
          <div className="text-center py-12">
            <div className="text-slate-400 text-lg mb-2">لا توجد طلبات</div>
            <div className="text-slate-500 text-sm">
              {activeFilter === "all" && "لم تقم بأي طلبات بعد"}
              {activeFilter === "completed" && "لا توجد طلبات مكتملة"}
              {activeFilter === "pending" && "لا توجد طلبات قيد المعالجة"}
              {activeFilter === "failed" && "لا توجد طلبات فاشلة"}
            </div>
          </div>
        ) : (
          renderOrdersList()
        )}
      </CardContent>
    </Card>
  )
}
