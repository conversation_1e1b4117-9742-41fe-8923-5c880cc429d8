"use client"

import { Transaction } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"
import { 
  TrendingUp, 
  TrendingDown, 
  ShoppingCart, 
  Clock, 
  CheckCircle, 
  XCircle 
} from "lucide-react"

interface TransactionItemProps {
  transaction: Transaction
}

export function TransactionItem({ transaction }: TransactionItemProps) {
  const getTransactionIcon = () => {
    switch (transaction.type) {
      case "deposit":
        return <TrendingUp className="h-5 w-5 text-green-400" />
      case "withdrawal":
        return <TrendingDown className="h-5 w-5 text-red-400" />
      case "purchase":
        return <ShoppingCart className="h-5 w-5 text-blue-400" />
      default:
        return <Clock className="h-5 w-5 text-slate-400" />
    }
  }

  const getStatusIcon = () => {
    switch (transaction.status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-400" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return null
    }
  }

  const getTransactionTypeText = () => {
    switch (transaction.type) {
      case "deposit":
        return "إيداع"
      case "withdrawal":
        return "سحب"
      case "purchase":
        return "شراء"
      default:
        return "معاملة"
    }
  }

  const getStatusText = () => {
    switch (transaction.status) {
      case "completed":
        return "مكتملة"
      case "pending":
        return "قيد المعالجة"
      case "failed":
        return "فاشلة"
      default:
        return "غير معروف"
    }
  }

  const getAmountColor = () => {
    switch (transaction.type) {
      case "deposit":
        return "text-green-400"
      case "withdrawal":
        return "text-red-400"
      case "purchase":
        return "text-blue-400"
      default:
        return "text-slate-300"
    }
  }

  const getAmountPrefix = () => {
    switch (transaction.type) {
      case "deposit":
        return "+"
      case "withdrawal":
      case "purchase":
        return "-"
      default:
        return ""
    }
  }

  const formatDate = (date: Date) => {
    // Use consistent date formatting to prevent hydration mismatch
    const dateObj = new Date(date)
    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')
    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')

    return `${day}/${month}/${year} ${hours}:${minutes}`
  }

  return (
    <div className={cn(
      "flex items-center gap-4 p-4 rounded-xl transition-all duration-300 hover:scale-[1.02]",
      "bg-slate-700/30 hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500/50"
    )}>
      {/* Transaction Icon */}
      <div className="flex-shrink-0">
        <div className="w-12 h-12 rounded-full bg-slate-600/50 flex items-center justify-center">
          {getTransactionIcon()}
        </div>
      </div>

      {/* Transaction Details */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-3">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-white truncate text-base">
              {transaction.description}
            </h4>
            <div className="flex flex-wrap items-center gap-2 mt-2">
              <span className="text-sm text-slate-400 font-medium">
                {getTransactionTypeText()}
              </span>
              <span className="text-slate-500">•</span>
              <span className="text-sm text-slate-400">
                {formatDate(transaction.date)}
              </span>
            </div>
            {transaction.reference && (
              <div className="text-xs text-slate-500 mt-2 bg-slate-700/30 px-2 py-1 rounded-md inline-block">
                المرجع: {transaction.reference}
              </div>
            )}
          </div>

          {/* Amount */}
          <div className="text-right lg:text-left flex-shrink-0 lg:min-w-[120px]">
            <div className={cn("font-bold text-lg lg:text-xl", getAmountColor())}>
              {getAmountPrefix()}{formatCurrency(transaction.amount, transaction.currency)}
            </div>
            <div className="flex items-center gap-1 justify-end lg:justify-start mt-1">
              {getStatusIcon()}
              <span className="text-xs text-slate-400 font-medium">
                {getStatusText()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
