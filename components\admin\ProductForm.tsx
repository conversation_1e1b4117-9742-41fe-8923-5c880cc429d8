"use client"

import React, { useState, use<PERSON><PERSON><PERSON>, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"

import {
  Save,
  Eye,
  Settings,
  Layout,
  Palette,
  Plus,
  Trash2,
  GripVertical,
  Monitor,
  Smartphone,
  X,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"
import { DynamicField, ProductTemplate, FieldType } from "@/lib/types"
import { validateT<PERSON>plate, TemplateValidationResult } from "@/lib/validation"
import { FieldEditor } from "./FieldEditor"
import { ProductPreview } from "./ProductPreview"

interface ProductFormProps {
  template?: ProductTemplate
  onSave: (template: ProductTemplate) => void
  onPreview: (template: ProductTemplate) => void
  onClose: () => void
  isLoading?: boolean
}

export function ProductForm({ template, onSave, onPreview, onClose, isLoading = false }: ProductFormProps) {
  // Form state
  const [formData, setFormData] = useState<Partial<ProductTemplate>>({
    name: template?.name || "",
    description: template?.description || "",
    category: template?.category || "",
    fields: template?.fields || [],
    layout: template?.layout || {
      sections: [],
      theme: {
        primaryColor: "#f59e0b",
        secondaryColor: "#3b82f6", 
        backgroundColor: "#0f172a",
        textColor: "#ffffff",
        borderRadius: "medium",
        shadows: true,
        animations: true
      }
    }
  })

  const [activeTab, setActiveTab] = useState<"basic" | "fields" | "preview">("basic")
  const [previewMode, setPreviewMode] = useState<"desktop" | "mobile">("desktop")
  const [interactivePreview, setInteractivePreview] = useState(false)

  // Validation and error states
  const [validationErrors, setValidationErrors] = useState<TemplateValidationResult>({
    isValid: true,
    errors: [],
    fieldErrors: {}
  })
  const [showValidationErrors, setShowValidationErrors] = useState(false)

  // Auto-save states
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [autoSaveStatus, setAutoSaveStatus] = useState<"idle" | "saving" | "saved" | "error">("idle")

  // Auto-save key for localStorage
  const autoSaveKey = `draft_template_${template?.id || 'new'}`

  // Load draft on component mount
  useEffect(() => {
    if (typeof window !== "undefined" && !template) {
      try {
        const savedDraft = localStorage.getItem(autoSaveKey)
        if (savedDraft) {
          const draft = JSON.parse(savedDraft)
          // Only load draft if it's newer than 1 hour
          const draftTime = new Date(draft.lastModified)
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)

          if (draftTime > oneHourAgo) {
            setFormData(draft.data)
            setLastSaved(draftTime)
            console.log("تم استرداد المسودة المحفوظة")
          }
        }
      } catch (error) {
        console.error("خطأ في تحميل المسودة:", error)
      }
    }
  }, [template, autoSaveKey])

  // Auto-save functionality
  useEffect(() => {
    if (!formData.name && !formData.description && (!formData.fields || formData.fields.length === 0)) {
      return // Don't save empty forms
    }

    const autoSaveTimer = setTimeout(() => {
      if (typeof window !== "undefined") {
        try {
          setAutoSaveStatus("saving")
          const draftData = {
            data: formData,
            lastModified: new Date().toISOString()
          }
          localStorage.setItem(autoSaveKey, JSON.stringify(draftData))
          setLastSaved(new Date())
          setAutoSaveStatus("saved")

          // Clear saved status after 3 seconds
          setTimeout(() => setAutoSaveStatus("idle"), 3000)
        } catch (error) {
          console.error("خطأ في الحفظ التلقائي:", error)
          setAutoSaveStatus("error")
          setTimeout(() => setAutoSaveStatus("idle"), 3000)
        }
      }
    }, 5000) // Auto-save after 5 seconds of inactivity

    return () => clearTimeout(autoSaveTimer)
  }, [formData, autoSaveKey])

  // Clear draft when form is saved successfully
  const clearDraft = useCallback(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.removeItem(autoSaveKey)
      } catch (error) {
        console.error("خطأ في حذف المسودة:", error)
      }
    }
  }, [autoSaveKey])

  // Handle basic form changes
  const handleBasicChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }, [])

  // Handle field operations
  const handleFieldAdd = useCallback((field: DynamicField) => {
    setFormData(prev => ({
      ...prev,
      fields: [...(prev.fields || []), field]
    }))
  }, [])

  const handleFieldUpdate = useCallback((fieldId: string, updates: Partial<DynamicField>) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields?.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      ) || []
    }))
  }, [])

  const handleFieldDelete = useCallback((fieldId: string) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields?.filter(field => field.id !== fieldId) || []
    }))
  }, [])

  const handleFieldReorder = useCallback((dragIndex: number, hoverIndex: number) => {
    setFormData(prev => {
      const fields = [...(prev.fields || [])]
      const dragField = fields[dragIndex]
      fields.splice(dragIndex, 1)
      fields.splice(hoverIndex, 0, dragField)
      
      // Update order values
      fields.forEach((field, index) => {
        field.order = index
      })
      
      return { ...prev, fields }
    })
  }, [])



  // Save template with validation
  const handleSave = useCallback(() => {
    // Validate template
    const validation = validateTemplate(formData)
    setValidationErrors(validation)

    if (!validation.isValid) {
      setShowValidationErrors(true)
      setActiveTab("basic") // Switch to basic tab to show errors
      return
    }

    const templateToSave: ProductTemplate = {
      id: template?.id || `template_${Date.now()}`,
      name: formData.name!,
      description: formData.description,
      category: formData.category!,
      fields: formData.fields || [],
      layout: formData.layout!,
      createdAt: template?.createdAt || new Date(),
      updatedAt: new Date()
    }

    // Clear draft after successful save
    clearDraft()
    onSave(templateToSave)
    onClose()
  }, [formData, template, onSave, onClose, clearDraft])

  // Preview template with basic validation
  const handlePreview = useCallback(() => {
    // Basic validation for preview
    if (!formData.name || !formData.category) {
      setValidationErrors({
        isValid: false,
        errors: ["يرجى ملء اسم القالب والفئة أولاً"],
        fieldErrors: {}
      })
      setShowValidationErrors(true)
      setActiveTab("basic")
      return
    }

    const templateToPreview: ProductTemplate = {
      id: template?.id || `preview_${Date.now()}`,
      name: formData.name!,
      description: formData.description,
      category: formData.category!,
      fields: formData.fields || [],
      layout: formData.layout!,
      createdAt: template?.createdAt || new Date(),
      updatedAt: new Date()
    }

    onPreview(templateToPreview)
  }, [formData, template, onPreview])

  return (
    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-slate-800 border border-slate-700 rounded-lg w-full max-w-6xl max-h-[98vh] sm:max-h-[95vh] overflow-hidden text-white relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 left-3 z-10 p-2 rounded-lg bg-slate-700/50 hover:bg-slate-600 transition-colors"
        >
          <X className="h-4 w-4 text-slate-300" />
        </button>

        {/* Header */}
        <div className="p-4 sm:p-6 pb-0">
          <div className="flex flex-col gap-4">
            <div>
              <h1 className="text-lg sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {template ? "تعديل قالب المنتج" : "إنشاء قالب منتج جديد"}
              </h1>
              <p className="text-slate-400 mt-1 text-sm sm:text-base">
                قم بإنشاء قوالب منتجات قابلة للتخصيص بالكامل
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={handlePreview}
                disabled={isLoading}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 w-full sm:w-auto"
              >
                <Eye className="h-4 w-4 ml-2" />
                معاينة
              </Button>
              <Button
                onClick={handleSave}
                disabled={isLoading || !formData.name || !formData.category}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 w-full sm:w-auto"
              >
                <Save className="h-4 w-4 ml-2" />
                {isLoading ? "جاري الحفظ..." : "حفظ القالب"}
              </Button>
            </div>

            {/* Auto-save status */}
            {autoSaveStatus !== "idle" && (
              <div className="flex items-center gap-2 text-sm">
                {autoSaveStatus === "saving" && (
                  <>
                    <Clock className="h-4 w-4 text-yellow-400 animate-spin" />
                    <span className="text-yellow-400">جاري الحفظ التلقائي...</span>
                  </>
                )}
                {autoSaveStatus === "saved" && (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-green-400">تم الحفظ التلقائي</span>
                    {lastSaved && (
                      <span className="text-slate-400">
                        ({lastSaved.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })})
                      </span>
                    )}
                  </>
                )}
                {autoSaveStatus === "error" && (
                  <>
                    <AlertCircle className="h-4 w-4 text-red-400" />
                    <span className="text-red-400">خطأ في الحفظ التلقائي</span>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Validation Errors */}
        {showValidationErrors && !validationErrors.isValid && (
          <div className="p-4 sm:p-6 pt-0">
            <Alert className="border-red-500 bg-red-500/10">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-300">
                <div className="space-y-2">
                  <p className="font-medium">يرجى إصلاح الأخطاء التالية:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {validationErrors.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                  {Object.keys(validationErrors.fieldErrors).length > 0 && (
                    <div className="mt-3">
                      <p className="font-medium mb-2">أخطاء في الحقول:</p>
                      {Object.entries(validationErrors.fieldErrors).map(([fieldKey, errors]) => (
                        <div key={fieldKey} className="mb-2">
                          <p className="text-sm font-medium">حقل {fieldKey.replace('field_', '')}:</p>
                          <ul className="list-disc list-inside ml-4 text-sm">
                            {errors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowValidationErrors(false)}
                    className="mt-3 border-red-400 text-red-300 hover:bg-red-500/20"
                  >
                    إخفاء الأخطاء
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Main Content */}
        <div className="p-4 sm:p-6 pt-0 max-h-[85vh] sm:max-h-[80vh] overflow-y-auto">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-4 sm:space-y-6">
            <TabsList className="grid w-full grid-cols-3 bg-slate-700/50 border-slate-600 text-xs sm:text-sm">
              <TabsTrigger value="basic" className="data-[state=active]:bg-slate-600">
                <Settings className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">الأساسيات</span>
                <span className="sm:hidden">أساسي</span>
              </TabsTrigger>
              <TabsTrigger value="fields" className="data-[state=active]:bg-slate-600">
                <Plus className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">الحقول</span>
                <span className="sm:hidden">حقول</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="data-[state=active]:bg-slate-600">
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">المعاينة</span>
                <span className="sm:hidden">معاينة</span>
              </TabsTrigger>
            </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="space-y-6">
            <Card className="bg-slate-700/50 border-slate-600/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="template-name" className="text-slate-300">
                      اسم القالب *
                    </Label>
                    <Input
                      id="template-name"
                      value={formData.name || ""}
                      onChange={(e) => handleBasicChange("name", e.target.value)}
                      placeholder="مثال: قالب منتجات الألعاب"
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="template-category" className="text-slate-300">
                      الفئة *
                    </Label>
                    <Select
                      value={formData.category || ""}
                      onValueChange={(value) => handleBasicChange("category", value)}
                    >
                      <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gaming">ألعاب</SelectItem>
                        <SelectItem value="digital">منتجات رقمية</SelectItem>
                        <SelectItem value="subscriptions">اشتراكات</SelectItem>
                        <SelectItem value="gift-cards">بطاقات هدايا</SelectItem>
                        <SelectItem value="other">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="template-description" className="text-slate-300">
                    وصف القالب
                  </Label>
                  <Textarea
                    id="template-description"
                    value={formData.description || ""}
                    onChange={(e) => handleBasicChange("description", e.target.value)}
                    placeholder="وصف مختصر للقالب وكيفية استخدامه..."
                    className="bg-slate-700/50 border-slate-600 text-white min-h-[100px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Fields Tab */}
          <TabsContent value="fields" className="space-y-6">
            <FieldEditor
              fields={formData.fields || []}
              onFieldAdd={handleFieldAdd}
              onFieldUpdate={handleFieldUpdate}
              onFieldDelete={handleFieldDelete}
              onFieldReorder={handleFieldReorder}
            />
          </TabsContent>



          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4">
              <h3 className="text-lg font-semibold text-white">معاينة المنتج</h3>
              <div className="flex items-center gap-4">
                {/* Interactive Toggle */}
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-300">تفاعلي</label>
                  <input
                    type="checkbox"
                    checked={interactivePreview}
                    onChange={(e) => setInteractivePreview(e.target.checked)}
                    className="rounded border-slate-600 bg-slate-700"
                  />
                </div>

                {/* Device Mode */}
                <div className="flex items-center gap-2">
                  <Button
                    variant={previewMode === "desktop" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreviewMode("desktop")}
                    className="border-slate-600"
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewMode === "mobile" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreviewMode("mobile")}
                    className="border-slate-600"
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            
            <ProductPreview
              template={{
                id: "preview",
                name: formData.name || "معاينة المنتج",
                category: formData.category || "gaming",
                fields: formData.fields || [],
                layout: formData.layout!,
                createdAt: new Date(),
                updatedAt: new Date()
              }}
              mode={previewMode}
              interactive={interactivePreview}
            />
          </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
