"use client"

import { useState } from "react"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { CurrencySelector } from "@/components/wallet/CurrencySelector"
import { CurrencyConverter } from "@/components/wallet/CurrencyConverter"
import { WalletBalance } from "@/components/wallet/WalletBalance"
import { WalletTransactions } from "@/components/wallet/WalletTransactions"
import { useWalletData } from "@/hooks/useWalletData"
import { mockWalletData } from "@/lib/data/mockWalletData"
import { Currency, CurrencyConversionRequest } from "@/lib/types"
import { Wallet } from "lucide-react"
import { useRouter } from "next/navigation"

export function WalletPage() {
  const [activeTab, setActiveTab] = useState("wallet") // Set to wallet tab since this is wallet page
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>("SDG")
  const [showConverter, setShowConverter] = useState(false)
  const router = useRouter()

  // Enhanced wallet data hook with multi-currency support
  const {
    walletData,
    availableCurrencies,
    conversionHistory,
    isLoading,
    isConverting,
    error,
    convertCurrency,
    getConversionPreview,
    updateCurrencyPreference,
    refreshData
  } = useWalletData("user-id") // TODO: Get actual user ID from auth

  // Enhanced handler for currency change with Supabase integration
  const handleCurrencyChange = async (currency: Currency) => {
    setSelectedCurrency(currency)
    try {
      await updateCurrencyPreference(currency)
    } catch (err) {
      console.error('Failed to update currency preference:', err)
    }
  }

  // Handle currency conversion
  const handleCurrencyConversion = async (
    fromCurrency: Currency,
    toCurrency: Currency,
    amount: number
  ) => {
    const request: CurrencyConversionRequest = {
      userId: "user-id", // TODO: Get actual user ID
      fromCurrency,
      toCurrency,
      amount,
      acceptFees: true
    }

    const result = await convertCurrency(request)
    if (result) {
      // Refresh data to show updated balances
      await refreshData()
      setShowConverter(false)
    }
    return result
  }

  // ## Handler for adding balance - navigates to checkout page
  const handleAddBalance = () => {
    router.push("/checkout")
  }

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <Wallet className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            محفظتي
          </h1>
          <p className="text-slate-300 text-lg">
            إدارة رصيدك ومعاملاتك المالية بسهولة وأمان
          </p>
        </div>

        {/* Currency Selector */}
        <div className="mb-8">
          <CurrencySelector
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            availableCurrencies={availableCurrencies}
            disabled={isLoading}
            showFullName={true}
          />
        </div>

        {/* Wallet Balance Section */}
        <div className="mb-8">
          <WalletBalance
            walletData={walletData}
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            onAddBalance={handleAddBalance}
            isLoading={isLoading}
          />
        </div>

        {/* Currency Converter Section */}
        {availableCurrencies.length > 1 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-white">Currency Converter</h2>
              <button
                onClick={() => setShowConverter(!showConverter)}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                {showConverter ? 'Hide' : 'Show'} Converter
              </button>
            </div>

            {showConverter && (
              <CurrencyConverter
                availableCurrencies={availableCurrencies}
                walletBalances={walletData.balances}
                onConvert={handleCurrencyConversion}
                onPreview={getConversionPreview}
                isLoading={isConverting}
              />
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg">
            <p className="text-red-100">{error}</p>
          </div>
        )}

        {/* Transactions Section */}
        <WalletTransactions
          transactions={walletData.transactions}
          selectedCurrency={selectedCurrency}
          isLoading={isLoading}
        />
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
