"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { EditProfileModal } from "@/components/profile/EditProfileModal"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/data/currencies"
import { formatDate } from "@/lib/utils/dateUtils"
import {
  User,
  Wallet,
  ShoppingBag,
  Edit3,
  Save,
  LogOut,
  Phone,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Camera,
  Star,
  Clock,
  CheckCircle
} from "lucide-react"

interface UserProfile {
  id: string
  accountId: string
  displayName: string
  firstName: string
  lastName: string
  email: string
  phone: string
  avatarUrl?: string
  walletBalance: {
    sdg: number
    egp: number
  }
  ordersCount: number
  joinDate: string
  lastLogin: string
  isVerified: boolean
}

interface ProfilePageProps {
  user: UserProfile
  onUpdateProfile?: (data: Partial<UserProfile>) => Promise<void>
  onLogout?: () => void
  onNavigateToWallet?: () => void
  onNavigateToOrders?: () => void
}

export function ProfilePage({ 
  user, 
  onUpdateProfile, 
  onLogout, 
  onNavigateToWallet, 
  onNavigateToOrders 
}: ProfilePageProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  
  // Form state
  const [formData, setFormData] = useState({
    firstName: user.firstName,
    lastName: user.lastName,
    displayName: user.displayName,
    email: user.email,
    phone: user.phone,
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSaveProfile = async () => {
    setIsLoading(true)
    try {
      // ## Validate passwords if changing
      if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
        throw new Error("كلمات المرور غير متطابقة")
      }

      // ## Call update function - will be replaced with Supabase profile update
      const updateData: Partial<UserProfile> = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        displayName: formData.displayName,
        email: formData.email,
        phone: formData.phone
      }

      await onUpdateProfile?.(updateData)
      setIsEditing(false)
      
      // Clear password fields
      setFormData(prev => ({
        ...prev,
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      }))
    } catch (error) {
      console.error("Profile update error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      displayName: user.displayName,
      email: user.email,
      phone: user.phone,
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    })
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-4xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            صفحة الحساب
          </h1>
          <p className="text-slate-300 text-lg">
            إدارة معلوماتك الشخصية وإعدادات حسابك
          </p>
        </div>

        {/* User Profile Card */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center gap-6">
              {/* Avatar */}
              <div className="relative">
                <Avatar className="h-24 w-24 border-4 border-yellow-400/20">
                  <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                  <AvatarFallback className="bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-2xl font-bold">
                    {user.displayName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="sm"
                  className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full bg-slate-700 hover:bg-slate-600 p-0"
                >
                  <Camera className="h-4 w-4" />
                </Button>
              </div>

              {/* User Info */}
              <div className="flex-1 text-center md:text-right">
                <div className="flex items-center justify-center md:justify-start gap-2 mb-2">
                  <h2 className="text-2xl font-bold text-white">
                    مرحبًا، {user.displayName}!
                  </h2>
                  {user.isVerified && (
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  )}
                </div>
                <p className="text-slate-300 mb-1">
                  رقم حسابك في رايه شوب: <span className="font-mono text-yellow-400">{user.accountId}</span>
                </p>
                <div className="flex items-center justify-center md:justify-start gap-4 text-sm text-slate-400">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    عضو منذ {formatDate(user.joinDate)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    آخر دخول {formatDate(user.lastLogin)}
                  </div>
                </div>
              </div>

              {/* Edit Button */}
              <div className="flex gap-2">
                <Button
                  onClick={() => setIsEditModalOpen(true)}
                  className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900"
                >
                  <Edit3 className="h-4 w-4 ml-1" />
                  تعديل الملف الشخصي
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Wallet Card */}
          <Card 
            className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm cursor-pointer hover:bg-slate-700/50 transition-colors"
            onClick={onNavigateToWallet}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-white font-bold text-lg mb-1">رصيد محفظتك</h3>
                  <div className="space-y-1">
                    <div className="text-yellow-400 font-bold">
                      {formatCurrency(user.walletBalance.sdg, "SDG")}
                    </div>
                    <div className="text-orange-400 font-bold">
                      {formatCurrency(user.walletBalance.egp, "EGP")}
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-yellow-400 to-orange-500 p-3 rounded-xl">
                  <Wallet className="h-8 w-8 text-slate-900" />
                </div>
              </div>
              <Button 
                variant="ghost" 
                className="w-full mt-4 text-yellow-400 hover:text-yellow-300 hover:bg-yellow-400/10"
              >
                اضغط هنا لعرض تفاصيل المحفظة
              </Button>
            </CardContent>
          </Card>

          {/* Orders Card */}
          <Card 
            className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm cursor-pointer hover:bg-slate-700/50 transition-colors"
            onClick={onNavigateToOrders}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-white font-bold text-lg mb-1">طلباتي</h3>
                  <div className="text-2xl font-bold text-blue-400">
                    {user.ordersCount}
                  </div>
                  <div className="text-slate-400 text-sm">
                    إجمالي الطلبات
                  </div>
                </div>
                <div className="bg-gradient-to-br from-blue-400 to-blue-600 p-3 rounded-xl">
                  <ShoppingBag className="h-8 w-8 text-white" />
                </div>
              </div>
              <Button 
                variant="ghost" 
                className="w-full mt-4 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10"
              >
                اضغط هنا لعرض طلباتك
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Profile Edit Form */}
        {isEditing && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <User className="h-6 w-6 text-yellow-400" />
                تعديل البيانات الشخصية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-slate-300 font-medium">الاسم الأول</Label>
                  <Input
                    value={formData.firstName}
                    onChange={(e) => handleInputChange("firstName", e.target.value)}
                    className="bg-slate-700/50 border-slate-600/50 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300 font-medium">الاسم الأخير</Label>
                  <Input
                    value={formData.lastName}
                    onChange={(e) => handleInputChange("lastName", e.target.value)}
                    className="bg-slate-700/50 border-slate-600/50 text-white"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-slate-300 font-medium">اسم العرض</Label>
                <Input
                  value={formData.displayName}
                  onChange={(e) => handleInputChange("displayName", e.target.value)}
                  className="bg-slate-700/50 border-slate-600/50 text-white"
                />
                <div className="text-slate-400 text-sm">
                  بهذه الطريقة سيتم عرض اسمك في الحسابات والمراجعات
                </div>
              </div>

              {/* Contact Information */}
              <Separator className="bg-slate-600/50" />

              <div className="space-y-4">
                <h4 className="text-white font-semibold flex items-center gap-2">
                  <Phone className="h-5 w-5 text-blue-400" />
                  معلومات الاتصال
                </h4>

                <div className="space-y-2">
                  <Label className="text-slate-300 font-medium">رقم الهاتف</Label>
                  <Input
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="bg-slate-700/50 border-slate-600/50 text-white"
                    placeholder="+249xxxxxxxxx"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300 font-medium">البريد الإلكتروني</Label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="bg-slate-700/50 border-slate-600/50 text-white"
                  />
                </div>
              </div>

              {/* Password Change */}
              <Separator className="bg-slate-600/50" />

              <div className="space-y-4">
                <h4 className="text-white font-semibold flex items-center gap-2">
                  <Lock className="h-5 w-5 text-red-400" />
                  تغيير كلمة المرور
                </h4>

                <div className="space-y-2">
                  <Label className="text-slate-300 font-medium">كلمة المرور الحالية</Label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={formData.currentPassword}
                      onChange={(e) => handleInputChange("currentPassword", e.target.value)}
                      className="bg-slate-700/50 border-slate-600/50 text-white pl-10"
                      placeholder="اترك الحقل فارغاً إذا لم تغير"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-slate-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-slate-400" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-slate-300 font-medium">كلمة المرور الجديدة</Label>
                    <div className="relative">
                      <Input
                        type={showNewPassword ? "text" : "password"}
                        value={formData.newPassword}
                        onChange={(e) => handleInputChange("newPassword", e.target.value)}
                        className="bg-slate-700/50 border-slate-600/50 text-white pl-10"
                        placeholder="اترك الحقل فارغاً إذا لم تغير"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute left-0 top-0 h-full px-3 hover:bg-transparent"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-4 w-4 text-slate-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-slate-400" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300 font-medium">تأكيد كلمة المرور الجديدة</Label>
                    <Input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                      className="bg-slate-700/50 border-slate-600/50 text-white"
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                    />
                  </div>
                </div>

                {formData.newPassword && formData.confirmPassword &&
                 formData.newPassword !== formData.confirmPassword && (
                  <div className="text-red-400 text-sm">
                    كلمات المرور غير متطابقة
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Account Actions */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white text-xl">إعدادات الحساب</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600/30">
              <div>
                <div className="text-white font-medium">حالة التحقق</div>
                <div className="text-slate-400 text-sm">
                  {user.isVerified ? "تم التحقق من حسابك" : "لم يتم التحقق من حسابك"}
                </div>
              </div>
              <Badge className={user.isVerified ? "bg-green-500" : "bg-yellow-500"}>
                {user.isVerified ? "محقق" : "غير محقق"}
              </Badge>
            </div>

            <Separator className="bg-slate-600/50" />

            <Button
              onClick={onLogout}
              variant="outline"
              className="w-full bg-red-500/10 border-red-500/50 text-red-400 hover:bg-red-500/20 hover:border-red-500"
            >
              <LogOut className="h-4 w-4 ml-2" />
              تسجيل الخروج
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Edit Profile Modal */}
      <EditProfileModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        user={user}
        onUpdateProfile={onUpdateProfile || (() => Promise.resolve())}
        isLoading={isLoading}
      />
    </div>
  )
}
