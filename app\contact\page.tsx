"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  Phone,
  Mail,
  MessageCircle,
  Clock,
  MapPin,
  Send,
  CheckCircle,
  HelpCircle,
  Shield,
  Zap,
  Users,
  Award,
  Target,
  Heart,
  ChevronDown,
  ChevronUp
} from "lucide-react"

export default function ContactPage() {
  const [activeTab, setActiveTab] = useState("support")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)
  const router = useRouter()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Handle form input change
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsSubmitting(false)
    setIsSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false)
      setFormData({ name: "", email: "", phone: "", subject: "", message: "" })
    }, 3000)
  }

  // FAQ data
  const faqData = [
    {
      question: "كم يستغرق وقت الشحن؟",
      answer: "معظم عمليات الشحن تتم خلال أقل من دقيقة واحدة للمنتجات الفورية، و 5-15 دقيقة للمنتجات اليدوية."
    },
    {
      question: "ما هي طرق الدفع المتاحة؟",
      answer: "نقبل الدفع عبر البنك الأهلي، بنك فيصل الإسلامي، بنك الخرطوم، والمحافظ الإلكترونية مثل بنكك."
    },
    {
      question: "هل يمكنني استرداد أموالي؟",
      answer: "نعم، يمكن استرداد الأموال في حالة عدم تسليم المنتج خلال الوقت المحدد أو في حالة وجود خطأ في الطلب."
    },
    {
      question: "كيف أتأكد من صحة معرف اللاعب؟",
      answer: "تأكد من نسخ معرف اللاعب من داخل اللعبة مباشرة، وتحقق من اختيار السيرفر الصحيح قبل إتمام الطلب."
    },
    {
      question: "ماذا أفعل إذا لم أستلم الشحن؟",
      answer: "تواصل معنا فوراً عبر الواتساب أو نموذج الاتصال، وسنقوم بحل المشكلة خلال 24 ساعة."
    },
    {
      question: "هل خدماتكم متاحة 24/7؟",
      answer: "نعم، موقعنا متاح 24/7 للطلبات، والدعم الفني متاح من 8 صباحاً حتى 12 منتصف الليل."
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
      
      {/* Standard Navigation */}
      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />

      <SideMenu
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
      />
      
      <div className="relative z-10 container mx-auto px-4 py-8 max-w-7xl pt-32 pb-32">
        
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            اتصل بنا
          </h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            نحن هنا لمساعدتك! تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          
          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
                  <Phone className="h-6 w-6 text-yellow-400" />
                  معلومات الاتصال
                </h2>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg">
                    <MessageCircle className="h-5 w-5 text-green-400" />
                    <div>
                      <p className="text-white font-medium">واتساب</p>
                      <p className="text-slate-400">+249 123 456 789</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-400" />
                    <div>
                      <p className="text-white font-medium">البريد الإلكتروني</p>
                      <p className="text-slate-400"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg">
                    <Clock className="h-5 w-5 text-purple-400" />
                    <div>
                      <p className="text-white font-medium">ساعات العمل</p>
                      <p className="text-slate-400">8:00 ص - 12:00 م (يومياً)</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-lg">
                    <MapPin className="h-5 w-5 text-red-400" />
                    <div>
                      <p className="text-white font-medium">الموقع</p>
                      <p className="text-slate-400">الخرطوم، السودان</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-white mb-4">تواصل سريع</h3>
                <div className="space-y-3">
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                    onClick={() => window.open("https://wa.me/249123456789", "_blank")}
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    واتساب
                  </Button>
                  
                  <Button 
                    variant="outline"
                    className="w-full border-slate-600 text-white hover:bg-slate-700"
                    onClick={() => window.location.href = "mailto:<EMAIL>"}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    إرسال إيميل
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
                <Send className="h-6 w-6 text-yellow-400" />
                أرسل لنا رسالة
              </h2>
              
              {isSubmitted ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">تم إرسال رسالتك بنجاح!</h3>
                  <p className="text-slate-400">سنتواصل معك قريباً</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-slate-300 mb-2 block">
                        الاسم *
                      </Label>
                      <Input
                        id="name"
                        required
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="اسمك الكامل"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="phone" className="text-slate-300 mb-2 block">
                        رقم الهاتف *
                      </Label>
                      <Input
                        id="phone"
                        required
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="+249xxxxxxxxx"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-slate-300 mb-2 block">
                      البريد الإلكتروني
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="subject" className="text-slate-300 mb-2 block">
                      الموضوع *
                    </Label>
                    <Input
                      id="subject"
                      required
                      value={formData.subject}
                      onChange={(e) => handleInputChange("subject", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="موضوع رسالتك"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="message" className="text-slate-300 mb-2 block">
                      الرسالة *
                    </Label>
                    <Textarea
                      id="message"
                      required
                      rows={4}
                      value={formData.message}
                      onChange={(e) => handleInputChange("message", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white resize-none"
                      placeholder="اكتب رسالتك هنا..."
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-900 mr-2"></div>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        إرسال الرسالة
                      </>
                    )}
                  </Button>
                </form>
              )}
            </CardContent>
          </Card>
        </div>

        {/* About Us Section */}
        <div className="mb-16">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
                  من نحن
                </h2>
                <p className="text-xl text-slate-300">
                  رايه شوب - رائدون في عالم الألعاب الرقمية
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-yellow-400/20 p-3 rounded-lg">
                      <Target className="h-6 w-6 text-yellow-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">رؤيتنا</h3>
                      <p className="text-slate-300">
                        أن نكون المتجر الرقمي الأول في السودان والمنطقة لشحن الألعاب والخدمات الرقمية،
                        نقدم أفضل تجربة للاعبين مع أسرع خدمة وأفضل الأسعار.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-blue-400/20 p-3 rounded-lg">
                      <Heart className="h-6 w-6 text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">مهمتنا</h3>
                      <p className="text-slate-300">
                        تمكين اللاعبين من الاستمتاع بألعابهم المفضلة من خلال توفير خدمات شحن سريعة وآمنة وموثوقة،
                        مع دعم فني متميز على مدار الساعة.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-green-400/20 p-3 rounded-lg">
                      <Award className="h-6 w-6 text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">قيمنا</h3>
                      <ul className="text-slate-300 space-y-1">
                        <li>• الثقة والشفافية في جميع التعاملات</li>
                        <li>• السرعة والكفاءة في تقديم الخدمات</li>
                        <li>• الابتكار المستمر لتحسين التجربة</li>
                        <li>• دعم المجتمع المحلي للألعاب</li>
                      </ul>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-purple-400/20 p-3 rounded-lg">
                      <Users className="h-6 w-6 text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">فريقنا</h3>
                      <p className="text-slate-300">
                        فريق من المتخصصين والمتحمسين للألعاب، نعمل بجد لضمان حصولك على أفضل تجربة شحن ممكنة.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-400 mb-1">+10K</div>
                  <div className="text-slate-400 text-sm">عميل راضي</div>
                </div>
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-green-400 mb-1">99%</div>
                  <div className="text-slate-400 text-sm">نجاح العمليات</div>
                </div>
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-blue-400 mb-1">&lt;1 دقيقة</div>
                  <div className="text-slate-400 text-sm">متوسط الشحن</div>
                </div>
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-purple-400 mb-1">24/7</div>
                  <div className="text-slate-400 text-sm">دعم فني</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Section */}
        <div className="mb-16">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4 flex items-center justify-center gap-2">
                  <HelpCircle className="h-8 w-8" />
                  الأسئلة الشائعة
                </h2>
                <p className="text-xl text-slate-300">
                  إجابات على أكثر الأسئلة شيوعاً
                </p>
              </div>

              <div className="space-y-4">
                {faqData.map((faq, index) => (
                  <div key={index} className="border border-slate-700/50 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                      className="w-full p-4 text-right bg-slate-700/30 hover:bg-slate-700/50 transition-colors duration-300 flex items-center justify-between"
                    >
                      <span className="text-white font-medium">{faq.question}</span>
                      {expandedFaq === index ? (
                        <ChevronUp className="h-5 w-5 text-yellow-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-slate-400" />
                      )}
                    </button>

                    {expandedFaq === index && (
                      <div className="p-4 bg-slate-800/30 border-t border-slate-700/50">
                        <p className="text-slate-300 leading-relaxed">{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-8 text-center">
                <p className="text-slate-400 mb-4">لم تجد إجابة لسؤالك؟</p>
                <Button
                  onClick={() => window.open("https://wa.me/249123456789", "_blank")}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  تواصل معنا مباشرة
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-bold text-white mb-2">أمان مضمون</h3>
              <p className="text-slate-400 text-sm">
                جميع المعاملات محمية بأعلى معايير الأمان
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Zap className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-bold text-white mb-2">شحن فوري</h3>
              <p className="text-slate-400 text-sm">
                معظم عمليات الشحن تتم خلال أقل من دقيقة
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm text-center">
            <CardContent className="p-6">
              <Users className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-bold text-white mb-2">دعم متميز</h3>
              <p className="text-slate-400 text-sm">
                فريق دعم متخصص متاح على مدار الساعة
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Standard Mobile Navigation */}
      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
