-- =====================================================
-- Al-Raya Store Multi-Currency Platform Migration
-- Phase 3: Row Level Security (RLS) Policies
-- =====================================================

-- =====================================================
-- 1. ENABLE RLS ON ALL TABLES
-- =====================================================
ALTER TABLE currencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_currency_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE currency_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallets_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_orders_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 2. CURRENCIES TABLE POLICIES
-- =====================================================
-- Allow everyone to read active currencies
CREATE POLICY "Allow read access to active currencies" ON currencies
  FOR SELECT USING (is_active = true);

-- Allow admins to manage currencies
CREATE POLICY "Allow admin full access to currencies" ON currencies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- =====================================================
-- 3. EXCHANGE RATES TABLE POLICIES
-- =====================================================
-- Allow everyone to read active exchange rates
CREATE POLICY "Allow read access to active exchange rates" ON exchange_rates
  FOR SELECT USING (is_active = true);

-- Allow admins to manage exchange rates
CREATE POLICY "Allow admin full access to exchange rates" ON exchange_rates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- =====================================================
-- 4. CLIENT CURRENCY SETTINGS POLICIES
-- =====================================================
-- Allow everyone to read client currency settings (for single tenant)
CREATE POLICY "Allow read access to client currency settings" ON client_currency_settings
  FOR SELECT USING (client_id IS NULL OR client_id = auth.uid());

-- Allow admins to manage client currency settings
CREATE POLICY "Allow admin full access to client currency settings" ON client_currency_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- =====================================================
-- 5. CURRENCY AUDIT LOG POLICIES
-- =====================================================
-- Allow admins to read audit logs
CREATE POLICY "Allow admin read access to audit logs" ON currency_audit_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- Allow system to insert audit logs
CREATE POLICY "Allow system insert to audit logs" ON currency_audit_log
  FOR INSERT WITH CHECK (true);

-- =====================================================
-- 6. USER WALLETS POLICIES
-- =====================================================
-- Users can only access their own wallets
CREATE POLICY "Users can access own wallets" ON user_wallets_new
  FOR ALL USING (auth.uid() = user_id);

-- Admins can access all wallets
CREATE POLICY "Admins can access all wallets" ON user_wallets_new
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- =====================================================
-- 7. WALLET TRANSACTIONS POLICIES
-- =====================================================
-- Users can access their own transactions
CREATE POLICY "Users can access own transactions" ON wallet_transactions_new
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own transactions (for deposits)
CREATE POLICY "Users can insert own transactions" ON wallet_transactions_new
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can access all transactions
CREATE POLICY "Admins can access all transactions" ON wallet_transactions_new
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- System can update transaction status
CREATE POLICY "System can update transaction status" ON wallet_transactions_new
  FOR UPDATE USING (true)
  WITH CHECK (true);

-- =====================================================
-- 8. PRODUCT ORDERS POLICIES
-- =====================================================
-- Users can access their own orders
CREATE POLICY "Users can access own orders" ON product_orders_new
  FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own orders
CREATE POLICY "Users can create own orders" ON product_orders_new
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can access all orders
CREATE POLICY "Admins can access all orders" ON product_orders_new
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- System can update order status
CREATE POLICY "System can update order status" ON product_orders_new
  FOR UPDATE USING (true)
  WITH CHECK (true);

-- =====================================================
-- 9. USER PREFERENCES POLICIES
-- =====================================================
-- Users can access their own preferences
CREATE POLICY "Users can access own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

-- Admins can access all preferences
CREATE POLICY "Admins can access all preferences" ON user_preferences
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
           OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
    )
  );

-- =====================================================
-- 10. HELPER FUNCTIONS FOR ROLE CHECKING
-- =====================================================
-- Function to check if current user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND (auth.users.raw_user_meta_data->>'role' = 'admin' 
         OR auth.users.raw_user_meta_data->>'role' = 'super_admin')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT auth.users.raw_user_meta_data->>'role'
    FROM auth.users 
    WHERE auth.users.id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 11. GRANT NECESSARY PERMISSIONS
-- =====================================================
-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
