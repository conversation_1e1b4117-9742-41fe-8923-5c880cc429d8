/**
 * ## Admin Chat Demo Page
 * Demonstrates different ways to integrate chat into admin dashboard
 * Shows floating button, widget, and notification bar implementations
 */

'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AdminChatButton, 
  AdminChatWidget, 
  AdminChatNotification 
} from '@/components/chat/AdminChatButton'
import { AdminChatModal } from '@/components/chat/AdminChatModal'
import { 
  BarChart3, 
  Users, 
  Package, 
  TrendingUp,
  MessageSquare,
  Settings,
  Bell
} from 'lucide-react'

export default function AdminChatDemoPage() {
  const [showChatModal, setShowChatModal] = useState(false)
  const [selectedDemo, setSelectedDemo] = useState<'floating' | 'widget' | 'notification' | 'modal'>('floating')

  // Mock admin data
  const adminData = {
    userId: 'admin-demo',
    userName: 'مدير النظام',
    userEmail: '<EMAIL>'
  }

  // Mock dashboard stats
  const dashboardStats = [
    { title: 'إجمالي الطلبات', value: '1,234', icon: Package, color: 'text-blue-400' },
    { title: 'العملاء النشطين', value: '89', icon: Users, color: 'text-green-400' },
    { title: 'المبيعات اليوم', value: '12,500 ر.س', icon: TrendingUp, color: 'text-yellow-400' },
    { title: 'معدل النمو', value: '+15%', icon: BarChart3, color: 'text-purple-400' },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            لوحة تحكم المدير - عرض توضيحي للمحادثات
          </h1>
          <p className="text-slate-400">
            تجربة أنواع مختلفة من تكامل نظام المحادثات في لوحة التحكم
          </p>
        </div>

        {/* Demo Selector */}
        <Card className="bg-slate-800/50 border-slate-700/50 mb-8">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              أنواع تكامل المحادثات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button
                variant={selectedDemo === 'floating' ? 'default' : 'outline'}
                onClick={() => setSelectedDemo('floating')}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <MessageSquare className="h-4 w-4 text-white" />
                </div>
                <div className="text-center">
                  <div className="font-medium">زر عائم</div>
                  <div className="text-xs text-slate-400">أسفل الشاشة</div>
                </div>
              </Button>

              <Button
                variant={selectedDemo === 'widget' ? 'default' : 'outline'}
                onClick={() => setSelectedDemo('widget')}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <Settings className="h-4 w-4 text-white" />
                </div>
                <div className="text-center">
                  <div className="font-medium">ودجت مدمج</div>
                  <div className="text-xs text-slate-400">في اللوحة</div>
                </div>
              </Button>

              <Button
                variant={selectedDemo === 'notification' ? 'default' : 'outline'}
                onClick={() => setSelectedDemo('notification')}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <Bell className="h-4 w-4 text-white" />
                </div>
                <div className="text-center">
                  <div className="font-medium">شريط إشعارات</div>
                  <div className="text-xs text-slate-400">في الأعلى</div>
                </div>
              </Button>

              <Button
                variant={selectedDemo === 'modal' ? 'default' : 'outline'}
                onClick={() => setSelectedDemo('modal')}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <MessageSquare className="h-4 w-4 text-white" />
                </div>
                <div className="text-center">
                  <div className="font-medium">نافذة منبثقة</div>
                  <div className="text-xs text-slate-400">مخصصة</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Notification Bar Demo */}
        {selectedDemo === 'notification' && (
          <div className="mb-6">
            <AdminChatNotification
              userId={adminData.userId}
              onOpenChat={() => setShowChatModal(true)}
              className="mb-4"
            />
          </div>
        )}

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardStats.map((stat, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400 mb-1">{stat.title}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Dashboard Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Recent Orders */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">الطلبات الأخيرة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((order) => (
                    <div key={order} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                          {order}
                        </div>
                        <div>
                          <p className="text-white font-medium">طلب #PRD-202501-00{order}</p>
                          <p className="text-slate-400 text-sm">شحن كلاش أوف كلانز - 500 جوهرة</p>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className="text-green-400 font-medium">150 ر.س</p>
                        <p className="text-slate-400 text-sm">منذ {order} ساعات</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Chart Placeholder */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">أداء المبيعات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-slate-700/30 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                    <p className="text-slate-400">مخطط الأداء</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            
            {/* Chat Widget Demo */}
            {selectedDemo === 'widget' && (
              <AdminChatWidget
                userId={adminData.userId}
                userName={adminData.userName}
                userEmail={adminData.userEmail}
                className="mb-6"
              />
            )}

            {/* Quick Actions */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  إضافة منتج جديد
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  إدارة العملاء
                </Button>
                <Button 
                  className="w-full justify-start" 
                  variant="outline"
                  onClick={() => setShowChatModal(true)}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  فتح المحادثات
                </Button>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">حالة النظام</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">الخادم</span>
                  <Badge className="bg-green-500/20 text-green-400">متصل</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">قاعدة البيانات</span>
                  <Badge className="bg-green-500/20 text-green-400">متصل</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">المحادثات</span>
                  <Badge className="bg-green-500/20 text-green-400">نشط</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">المدفوعات</span>
                  <Badge className="bg-green-500/20 text-green-400">متصل</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Floating Chat Button Demo */}
        {selectedDemo === 'floating' && (
          <AdminChatButton
            userId={adminData.userId}
            userName={adminData.userName}
            userEmail={adminData.userEmail}
            position="bottom-right"
          />
        )}

        {/* Custom Modal Demo */}
        {selectedDemo === 'modal' && (
          <div className="fixed bottom-6 right-6 z-40">
            <Button
              onClick={() => setShowChatModal(true)}
              className="w-14 h-14 rounded-full bg-purple-500 hover:bg-purple-600 text-white shadow-lg"
            >
              <MessageSquare className="w-6 h-6" />
            </Button>
          </div>
        )}

        {/* Custom Chat Modal */}
        <AdminChatModal
          isOpen={showChatModal}
          onClose={() => setShowChatModal(false)}
          userId={adminData.userId}
          userName={adminData.userName}
          userEmail={adminData.userEmail}
          position="center"
        />

        {/* Instructions */}
        <Card className="bg-slate-800/50 border-slate-700/50 mt-8">
          <CardHeader>
            <CardTitle className="text-white">تعليمات التكامل</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-slate-300">
              <div>
                <h4 className="font-medium text-white mb-2">الزر العائم (موصى به)</h4>
                <code className="bg-slate-700 p-2 rounded text-sm block">
                  {`<AdminChatButton userId="admin-123" position="bottom-right" />`}
                </code>
              </div>
              
              <div>
                <h4 className="font-medium text-white mb-2">الودجت المدمج</h4>
                <code className="bg-slate-700 p-2 rounded text-sm block">
                  {`<AdminChatWidget userId="admin-123" className="col-span-1" />`}
                </code>
              </div>
              
              <div>
                <h4 className="font-medium text-white mb-2">شريط الإشعارات</h4>
                <code className="bg-slate-700 p-2 rounded text-sm block">
                  {`<AdminChatNotification userId="admin-123" onOpenChat={() => setShowChat(true)} />`}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
