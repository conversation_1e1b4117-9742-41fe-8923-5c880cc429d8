/**
 * ## Global Chat Provider
 * Provides chat functionality across all pages of the website
 * Automatically detects user role and shows appropriate chat interface
 */

'use client'

import { useState, useEffect, createContext, useContext } from 'react'
import { ChatModal } from './AdminChatModal'

interface ChatUser {
  id: string
  name: string
  email: string
  role: 'admin' | 'customer' | 'guest'
}

interface GlobalChatContextType {
  user: ChatUser | null
  isEnabled: boolean
  isOpen: boolean
  setUser: (user: ChatUser | null) => void
  setEnabled: (enabled: boolean) => void
  openChat: () => void
  closeChat: () => void
}

const GlobalChatContext = createContext<GlobalChatContextType | null>(null)

export function useGlobalChat() {
  const context = useContext(GlobalChatContext)
  if (!context) {
    throw new Error('useGlobalChat must be used within GlobalChatProvider')
  }
  return context
}

interface GlobalChatProviderProps {
  children: React.ReactNode
}

export function GlobalChatProvider({ children }: GlobalChatProviderProps) {
  const [user, setUser] = useState<ChatUser | null>(null)
  const [isEnabled, setEnabled] = useState(true)
  const [isOpen, setIsOpen] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  /**
   * ## Initialize User Detection
   * Detects if user is admin or customer based on current route and auth
   */
  useEffect(() => {
    const initializeUser = async () => {
      try {
        // ## TODO: Replace with actual Supabase auth
        // const { data: { user }, error } = await supabase.auth.getUser()
        // if (user) {
        //   const { data: profile } = await supabase
        //     .from('profiles')
        //     .select('role, full_name')
        //     .eq('id', user.id)
        //     .single()
        //   
        //   setUser({
        //     id: user.id,
        //     name: profile?.full_name || user.email || 'مستخدم',
        //     email: user.email || '',
        //     role: profile?.role || 'customer'
        //   })
        // }

        // ## TODO: Implement real user detection
        // For now, create a demo customer user
        setUser({
          id: 'customer-demo',
          name: 'عميل',
          email: '<EMAIL>',
          role: 'customer'
        })
      } catch (error) {
        console.error('Error initializing chat user:', error)
        // ## TODO: Handle authentication errors properly
        setUser({
          id: 'guest-' + Date.now(),
          name: 'زائر',
          email: '',
          role: 'guest'
        })
      } finally {
        setIsInitialized(true)
      }
    }

    initializeUser()
  }, [])

  // Chat control functions
  const openChat = () => setIsOpen(true)
  const closeChat = () => setIsOpen(false)

  if (!isInitialized) {
    return <>{children}</>
  }

  return (
    <GlobalChatContext.Provider value={{
      user,
      isEnabled,
      isOpen,
      setUser,
      setEnabled,
      openChat,
      closeChat
    }}>
      {children}

      {/* Global Customer Chat Modal */}
      {user && user.role !== 'admin' && (
        <ChatModal
          isOpen={isOpen}
          onClose={closeChat}
          userId={user.id}
          userName={user.name}
          userEmail={user.email}
          userRole="customer"
          position="center"
        />
      )}
    </GlobalChatContext.Provider>
  )
}
