/**
 * ## Main Chat System Component
 * Dynamic component that adapts based on user role
 * Handles routing between customer and admin interfaces
 */

'use client'

import { useState, useEffect } from 'react'
import { AdminChatInterface } from './AdminChatInterface'
import { CustomerChatInterface } from './CustomerChatInterface'
import { Card } from '@/components/ui/card'
import { Loader2, WifiOff } from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'

interface ChatSystemProps {
  userRole: 'customer' | 'admin'
  userId: string
  className?: string
  // ## Supabase Integration: User data will come from auth context
  userEmail?: string
  userName?: string
}

export function ChatSystem({ 
  userRole, 
  userId, 
  className = '',
  userEmail,
  userName 
}: ChatSystemProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  // Initialize chat system
  const { isConnected, error } = useChat({
    userId,
    userType: userRole
  })

  /**
   * ## Security Check: Validate User Role
   * Ensures user has proper permissions for chat access
   */
  useEffect(() => {
    const validateUserAccess = async () => {
      try {
        // ## TODO: Replace with Supabase auth check
        // const { data: { user }, error } = await supabase.auth.getUser()
        // if (error || !user) {
        //   throw new Error('غير مصرح بالوصول')
        // }
        
        // ## TODO: Check user role from database
        // const { data: profile } = await supabase
        //   .from('profiles')
        //   .select('role')
        //   .eq('id', user.id)
        //   .single()
        
        // if (profile?.role !== userRole) {
        //   throw new Error('صلاحيات غير صحيحة')
        // }
        
        setIsInitialized(true)
      } catch (err) {
        setConnectionError(err instanceof Error ? err.message : 'خطأ في التحقق من الصلاحيات')
      }
    }

    validateUserAccess()
  }, [userRole, userId])

  /**
   * ## Loading State
   */
  if (!isInitialized) {
    return (
      <Card className={`bg-slate-800/50 border-slate-700/50 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
            <p className="text-slate-300">جاري تحميل نظام المحادثة...</p>
          </div>
        </div>
      </Card>
    )
  }

  /**
   * ## Connection Error State
   */
  if (connectionError || error) {
    return (
      <Card className={`bg-slate-800/50 border-slate-700/50 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <WifiOff className="h-8 w-8 text-red-400 mx-auto mb-4" />
            <p className="text-red-300 mb-2">خطأ في الاتصال</p>
            <p className="text-slate-400 text-sm">
              {connectionError || error}
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </Card>
    )
  }

  /**
   * ## Connection Status Indicator
   */
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-xs text-slate-400 mb-2">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
      <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
    </div>
  )

  /**
   * ## Render Appropriate Interface
   */
  return (
    <div className={`chat-system ${className}`}>
      <ConnectionStatus />
      
      {userRole === 'admin' ? (
        <AdminChatInterface
          userId={userId}
          userName={userName}
          userEmail={userEmail}
        />
      ) : (
        <CustomerChatInterface
          userId={userId}
          userName={userName}
          userEmail={userEmail}
        />
      )}
    </div>
  )
}

/**
 * ## Export Customer Chat Interface for standalone use
 */
export { CustomerChatInterface } from './CustomerChatInterface'

/**
 * ## Chat System Provider (Optional)
 * For global chat state management across the app
 */
export function ChatProvider({ children }: { children: React.ReactNode }) {
  // ## TODO: Implement global chat context if needed
  // This could handle:
  // - Global unread count
  // - Cross-component notifications
  // - Shared connection state

  return <>{children}</>
}

/**
 * ## Chat Notification Badge Component
 * Reusable badge for showing unread counts in navigation
 */
interface ChatBadgeProps {
  count: number
  className?: string
}

export function ChatBadge({ count, className = '' }: ChatBadgeProps) {
  if (count === 0) return null
  
  return (
    <span className={`
      inline-flex items-center justify-center 
      min-w-[20px] h-5 px-1.5 
      bg-red-500 text-white text-xs font-medium 
      rounded-full animate-pulse
      ${className}
    `}>
      {count > 99 ? '99+' : count}
    </span>
  )
}

/**
 * ## Quick Chat Access Button
 * Floating action button for quick chat access
 */
interface QuickChatButtonProps {
  userRole: 'customer' | 'admin'
  unreadCount?: number
  onClick: () => void
}

export function QuickChatButton({ userRole, unreadCount = 0, onClick }: QuickChatButtonProps) {
  return (
    <button
      onClick={onClick}
      className="
        fixed bottom-6 right-6 z-50
        w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600
        hover:from-blue-600 hover:to-blue-700
        text-white rounded-full shadow-lg
        flex items-center justify-center
        transition-all duration-300 hover:scale-110
        focus:outline-none focus:ring-4 focus:ring-blue-500/30
      "
      aria-label={userRole === 'admin' ? 'إدارة المحادثات' : 'الدعم الفني'}
    >
      <svg 
        className="w-6 h-6" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" 
        />
      </svg>
      
      {unreadCount > 0 && (
        <ChatBadge 
          count={unreadCount} 
          className="absolute -top-2 -right-2" 
        />
      )}
    </button>
  )
}
