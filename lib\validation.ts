import { DynamicField, FieldValidation } from "./types"

export interface ValidationResult {
  isValid: boolean
  error?: string
}

export function validateField(field: DynamicField, value: any): ValidationResult {
  // Skip validation if field is not required and value is empty
  if (!field.required && (!value || value === "")) {
    return { isValid: true }
  }

  // Required field validation
  if (field.required && (!value || value === "")) {
    return {
      isValid: false,
      error: field.validation?.customMessage || `${field.label} مطلوب`
    }
  }

  // Type-specific validation
  switch (field.type) {
    case "email":
      return validateEmail(value, field.validation)
    
    case "number":
      return validateNumber(value, field.validation)

    case "quantity_selector":
      return validateQuantitySelector(value, field)

    case "text":
    case "textarea":
      return validateText(value, field.validation)

    case "select":
    case "radio":
      return validateSelection(value, field)

    case "image":
      return validateImage(value, field)

    case "checkbox":
      if (field.required && !value) {
        return {
          isValid: false,
          error: field.validation?.customMessage || `يجب الموافقة على ${field.label}`
        }
      }
      return { isValid: true }

    default:
      return { isValid: true }
  }
}

function validateEmail(value: string, validation?: FieldValidation): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!emailRegex.test(value)) {
    return {
      isValid: false,
      error: validation?.customMessage || "يرجى إدخال بريد إلكتروني صحيح"
    }
  }
  
  return { isValid: true }
}

function validateNumber(value: any, validation?: FieldValidation): ValidationResult {
  const numValue = Number(value)

  if (isNaN(numValue)) {
    return {
      isValid: false,
      error: validation?.customMessage || "يرجى إدخال رقم صحيح"
    }
  }

  if (validation?.min !== undefined && numValue < validation.min) {
    return {
      isValid: false,
      error: validation?.customMessage || `القيمة يجب أن تكون أكبر من أو تساوي ${validation.min}`
    }
  }

  if (validation?.max !== undefined && numValue > validation.max) {
    return {
      isValid: false,
      error: validation?.customMessage || `القيمة يجب أن تكون أقل من أو تساوي ${validation.max}`
    }
  }

  return { isValid: true }
}

function validateQuantitySelector(value: any, field: DynamicField): ValidationResult {
  const numValue = Number(value)

  // Check if value is a valid number
  if (isNaN(numValue)) {
    return {
      isValid: false,
      error: field.validation?.customMessage || "يرجى إدخال كمية صحيحة"
    }
  }

  // Check if value is a whole number (no decimals)
  if (!Number.isInteger(numValue)) {
    return {
      isValid: false,
      error: field.validation?.customMessage || "الكمية يجب أن تكون رقم صحيح"
    }
  }

  // Use field-specific min/max if available, otherwise fall back to validation
  const minValue = (field as any).min ?? field.validation?.min ?? 1
  const maxValue = (field as any).max ?? field.validation?.max

  if (numValue < minValue) {
    return {
      isValid: false,
      error: field.validation?.customMessage || `الكمية يجب أن تكون ${minValue} على الأقل`
    }
  }

  if (maxValue !== undefined && numValue > maxValue) {
    return {
      isValid: false,
      error: field.validation?.customMessage || `الكمية يجب أن تكون ${maxValue} كحد أقصى`
    }
  }

  return { isValid: true }
}

function validateText(value: string, validation?: FieldValidation): ValidationResult {
  if (validation?.min !== undefined && value.length < validation.min) {
    return {
      isValid: false,
      error: validation?.customMessage || `يجب أن يكون النص ${validation.min} أحرف على الأقل`
    }
  }
  
  if (validation?.max !== undefined && value.length > validation.max) {
    return {
      isValid: false,
      error: validation?.customMessage || `يجب أن يكون النص ${validation.max} حرف كحد أقصى`
    }
  }
  
  if (validation?.pattern) {
    try {
      const regex = new RegExp(validation.pattern)
      if (!regex.test(value)) {
        return {
          isValid: false,
          error: validation?.customMessage || "تنسيق النص غير صحيح"
        }
      }
    } catch (error) {
      console.warn("Invalid regex pattern:", validation.pattern)
    }
  }
  
  return { isValid: true }
}

function validateSelection(value: any, field: DynamicField): ValidationResult {
  if (!field.options || field.options.length === 0) {
    return { isValid: true }
  }
  
  const validOptions = field.options.map(opt => opt.value)
  
  if (!validOptions.includes(value)) {
    return {
      isValid: false,
      error: field.validation?.customMessage || "يرجى اختيار خيار صحيح"
    }
  }
  
  return { isValid: true }
}

function validateImage(value: any, field: DynamicField): ValidationResult {
  if (!value) {
    return { isValid: true }
  }
  
  // For now, we'll assume the value is a valid image URL or file
  // In a real implementation, you'd validate file size, type, etc.
  if (Array.isArray(value)) {
    if (!field.multiple) {
      return {
        isValid: false,
        error: field.validation?.customMessage || "هذا الحقل لا يدعم رفع عدة صور"
      }
    }
  }
  
  return { isValid: true }
}

// Validate entire form
export function validateForm(fields: DynamicField[], formData: Record<string, any>): {
  isValid: boolean
  errors: Record<string, string>
} {
  const errors: Record<string, string> = {}
  
  for (const field of fields) {
    if (!field.visible) continue
    
    const value = formData[field.name]
    const result = validateField(field, value)
    
    if (!result.isValid && result.error) {
      errors[field.name] = result.error
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Real-time validation hook
export function useFieldValidation(field: DynamicField, value: any) {
  const result = validateField(field, value)
  return result
}

// Template validation interface
export interface TemplateValidationResult {
  isValid: boolean
  errors: string[]
  fieldErrors: Record<string, string[]>
}

// Validate entire product template
export function validateTemplate(template: Partial<any>): TemplateValidationResult {
  const errors: string[] = []
  const fieldErrors: Record<string, string[]> = {}

  // Basic template validation
  if (!template.name?.trim()) {
    errors.push("اسم القالب مطلوب")
  }

  if (!template.category?.trim()) {
    errors.push("فئة القالب مطلوبة")
  }

  if (!template.fields || template.fields.length === 0) {
    errors.push("يجب إضافة حقل واحد على الأقل")
  }

  // Field validation
  if (template.fields && Array.isArray(template.fields)) {
    const fieldNames = new Set<string>()
    let hasVisibleFields = false

    template.fields.forEach((field: DynamicField, index: number) => {
      const fieldKey = `field_${index}`

      // Check for duplicate field names
      if (fieldNames.has(field.name)) {
        if (!fieldErrors[fieldKey]) fieldErrors[fieldKey] = []
        fieldErrors[fieldKey].push(`اسم الحقل "${field.name}" مكرر`)
      }
      fieldNames.add(field.name)

      // Check if at least one field is visible
      if (field.visible) {
        hasVisibleFields = true
      }

      // Validate field-specific configurations
      validateFieldConfiguration(field, fieldKey, fieldErrors)
    })

    if (!hasVisibleFields) {
      errors.push("يجب أن يكون حقل واحد على الأقل مرئياً")
    }
  }

  return {
    isValid: errors.length === 0 && Object.keys(fieldErrors).length === 0,
    errors,
    fieldErrors
  }
}

// Validate individual field configuration
function validateFieldConfiguration(field: DynamicField, fieldKey: string, fieldErrors: Record<string, string[]>) {
  if (!fieldErrors[fieldKey]) fieldErrors[fieldKey] = []

  // Basic field validation
  if (!field.label?.trim()) {
    fieldErrors[fieldKey].push("تسمية الحقل مطلوبة")
  }

  if (!field.name?.trim()) {
    fieldErrors[fieldKey].push("اسم الحقل التقني مطلوب")
  }

  // Field name format validation
  if (field.name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(field.name)) {
    fieldErrors[fieldKey].push("اسم الحقل التقني يجب أن يحتوي على أحرف إنجليزية وأرقام و _ فقط")
  }

  // Type-specific validation
  switch (field.type) {
    case "quantity_selector":
      validateQuantitySelectorConfig(field as any, fieldKey, fieldErrors)
      break
    case "package_selector":
      validatePackageSelectorConfig(field as any, fieldKey, fieldErrors)
      break
    case "select":
    case "radio":
      validateSelectFieldConfig(field as any, fieldKey, fieldErrors)
      break
    case "image":
      validateImageFieldConfig(field as any, fieldKey, fieldErrors)
      break
  }

  // Remove empty error arrays
  if (fieldErrors[fieldKey].length === 0) {
    delete fieldErrors[fieldKey]
  }
}

// Validate quantity selector configuration
function validateQuantitySelectorConfig(field: any, fieldKey: string, fieldErrors: Record<string, string[]>) {
  const min = field.min ?? 1
  const max = field.max
  const defaultValue = field.defaultValue ?? 1

  if (min < 1) {
    fieldErrors[fieldKey].push("الحد الأدنى للكمية يجب أن يكون 1 على الأقل")
  }

  if (max !== undefined && max < min) {
    fieldErrors[fieldKey].push("الحد الأقصى يجب أن يكون أكبر من أو يساوي الحد الأدنى")
  }

  if (defaultValue < min) {
    fieldErrors[fieldKey].push("القيمة الافتراضية يجب أن تكون أكبر من أو تساوي الحد الأدنى")
  }

  if (max !== undefined && defaultValue > max) {
    fieldErrors[fieldKey].push("القيمة الافتراضية يجب أن تكون أقل من أو تساوي الحد الأقصى")
  }
}

// Validate package selector configuration
function validatePackageSelectorConfig(field: any, fieldKey: string, fieldErrors: Record<string, string[]>) {
  if (!field.packages || field.packages.length === 0) {
    fieldErrors[fieldKey].push("يجب إضافة حزمة واحدة على الأقل")
    return
  }

  const packageIds = new Set<string>()
  field.packages.forEach((pkg: any, index: number) => {
    if (packageIds.has(pkg.id)) {
      fieldErrors[fieldKey].push(`معرف الحزمة "${pkg.id}" مكرر`)
    }
    packageIds.add(pkg.id)

    if (!pkg.name?.trim()) {
      fieldErrors[fieldKey].push(`اسم الحزمة ${index + 1} مطلوب`)
    }

    if (pkg.price < 0) {
      fieldErrors[fieldKey].push(`سعر الحزمة ${index + 1} لا يمكن أن يكون سالباً`)
    }

    if (pkg.originalPrice !== undefined && pkg.originalPrice < pkg.price) {
      fieldErrors[fieldKey].push(`السعر الأصلي للحزمة ${index + 1} يجب أن يكون أكبر من أو يساوي السعر الحالي`)
    }
  })
}

// Validate select field configuration
function validateSelectFieldConfig(field: any, fieldKey: string, fieldErrors: Record<string, string[]>) {
  if (!field.options || field.options.length === 0) {
    fieldErrors[fieldKey].push("يجب إضافة خيار واحد على الأقل")
    return
  }

  const optionValues = new Set<string>()
  field.options.forEach((option: any, index: number) => {
    if (optionValues.has(option.value)) {
      fieldErrors[fieldKey].push(`قيمة الخيار "${option.value}" مكررة`)
    }
    optionValues.add(option.value)

    if (!option.label?.trim()) {
      fieldErrors[fieldKey].push(`تسمية الخيار ${index + 1} مطلوبة`)
    }

    if (!option.value?.trim()) {
      fieldErrors[fieldKey].push(`قيمة الخيار ${index + 1} مطلوبة`)
    }
  })
}

// Validate image field configuration
function validateImageFieldConfig(field: any, fieldKey: string, fieldErrors: Record<string, string[]>) {
  if (field.maxSize && field.maxSize <= 0) {
    fieldErrors[fieldKey].push("الحد الأقصى لحجم الملف يجب أن يكون أكبر من صفر")
  }

  if (field.maxSize && field.maxSize > 50) {
    fieldErrors[fieldKey].push("الحد الأقصى لحجم الملف لا يمكن أن يتجاوز 50 ميجابايت")
  }
}
