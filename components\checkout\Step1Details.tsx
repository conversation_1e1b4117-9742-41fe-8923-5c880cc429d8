"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { AmountSelector } from "@/components/checkout/AmountSelector"
import { useCheckout } from "@/components/checkout/CheckoutContext"
import { ArrowLeft, Wallet } from "lucide-react"

interface Step1DetailsProps {
  onNext: () => void
}

export function Step1Details({ onNext }: Step1DetailsProps) {
  const { 
    state, 
    setAmount, 
    setCurrency, 
    canProceedToStep2 
  } = useCheckout()

  const handleNext = () => {
    if (canProceedToStep2()) {
      onNext()
    }
  }

  return (
    <div className="space-y-6">
      {/* Step Header */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <Wallet className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <CardTitle className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            تفاصيل الشحن
          </CardTitle>
          <p className="text-slate-300 text-base lg:text-lg">
            اختر مبلغ الشحن والعملة المطلوبة
          </p>
        </CardHeader>
      </Card>

      {/* Amount Selection */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardContent className="p-6 lg:p-8">
          <AmountSelector
            selectedAmount={state.amount}
            selectedCurrency={state.currency}
            onAmountChange={setAmount}
            onCurrencyChange={setCurrency}
          />
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="bg-slate-800/30 backdrop-blur-xl border-slate-700/30">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-yellow-400 mb-4">
            تعليمات مهمة
          </h3>
          <div className="space-y-3 text-slate-300">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
              <p>اختر المبلغ الذي تريد شحنه في محفظتك</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
              <p>يمكنك اختيار من المبالغ المحددة مسبقاً أو إدخال مبلغ مخصص</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
              <p>تأكد من اختيار العملة الصحيحة (جنيه سوداني أو مصري)</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
              <p>سيتم إضافة المبلغ إلى محفظتك بعد تأكيد الدفع</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end">
        <Button
          onClick={handleNext}
          disabled={!canProceedToStep2()}
          className={cn(
            "px-8 py-3 text-lg font-semibold transition-all duration-300",
            canProceedToStep2()
              ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 hover:from-yellow-500 hover:to-orange-600 hover:scale-105 shadow-lg hover:shadow-yellow-400/25"
              : "bg-slate-700 text-slate-400 cursor-not-allowed"
          )}
        >
          التالي
          <ArrowLeft className="h-5 w-5 mr-2" />
        </Button>
      </div>
    </div>
  )
}

// Import cn utility
import { cn } from "@/lib/utils"
