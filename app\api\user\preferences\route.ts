import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { Currency, UserCurrencyPreferences } from '@/lib/types'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

/**
 * GET /api/user/preferences
 * Get user currency preferences
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    const { data: preferences, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user preferences:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to fetch user preferences' 
        },
        { status: 500 }
      )
    }

    // If no preferences found, return defaults
    if (!preferences) {
      const defaultPreferences: Partial<UserCurrencyPreferences> = {
        userId,
        preferredCurrency: 'USD',
        displayCurrency: 'USD',
        enableCurrencyConversion: true,
        conversionConfirmationRequired: true
      }

      return NextResponse.json({
        success: true,
        preferences: defaultPreferences,
        isDefault: true
      })
    }

    // Transform database response to match interface
    const userPreferences: UserCurrencyPreferences = {
      id: preferences.id,
      userId: preferences.user_id,
      preferredCurrency: preferences.preferred_currency_code,
      displayCurrency: preferences.display_currency_code,
      enableCurrencyConversion: preferences.enable_currency_conversion,
      conversionConfirmationRequired: preferences.conversion_confirmation_required,
      preferences: preferences.preferences || {},
      createdAt: new Date(preferences.created_at),
      updatedAt: new Date(preferences.updated_at)
    }

    return NextResponse.json({
      success: true,
      preferences: userPreferences
    })

  } catch (error) {
    console.error('Unexpected error in GET /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/user/preferences
 * Create or update user currency preferences
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, preferences } = body

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    if (!preferences) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Preferences data is required' 
        },
        { status: 400 }
      )
    }

    // Validate currency codes
    const validCurrencies = ['USD', 'SDG', 'EGP', 'EUR', 'GBP', 'SAR', 'AED']
    
    if (preferences.preferredCurrency && !validCurrencies.includes(preferences.preferredCurrency)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid preferred currency' 
        },
        { status: 400 }
      )
    }

    if (preferences.displayCurrency && !validCurrencies.includes(preferences.displayCurrency)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid display currency' 
        },
        { status: 400 }
      )
    }

    // Prepare data for database
    const dbData = {
      user_id: userId,
      preferred_currency_code: preferences.preferredCurrency || 'USD',
      display_currency_code: preferences.displayCurrency || 'USD',
      enable_currency_conversion: preferences.enableCurrencyConversion !== false,
      conversion_confirmation_required: preferences.conversionConfirmationRequired !== false,
      preferences: preferences.preferences || {}
    }

    // Try to update existing preferences first
    const { data: existingPrefs } = await supabase
      .from('user_preferences')
      .select('id')
      .eq('user_id', userId)
      .single()

    let result
    if (existingPrefs) {
      // Update existing preferences
      result = await supabase
        .from('user_preferences')
        .update(dbData)
        .eq('user_id', userId)
        .select()
        .single()
    } else {
      // Create new preferences
      result = await supabase
        .from('user_preferences')
        .insert(dbData)
        .select()
        .single()
    }

    const { data, error } = result

    if (error) {
      console.error('Error saving user preferences:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to save user preferences',
          details: error.message 
        },
        { status: 500 }
      )
    }

    // Transform response to match interface
    const savedPreferences: UserCurrencyPreferences = {
      id: data.id,
      userId: data.user_id,
      preferredCurrency: data.preferred_currency_code,
      displayCurrency: data.display_currency_code,
      enableCurrencyConversion: data.enable_currency_conversion,
      conversionConfirmationRequired: data.conversion_confirmation_required,
      preferences: data.preferences || {},
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }

    return NextResponse.json({
      success: true,
      message: 'User preferences saved successfully',
      preferences: savedPreferences
    })

  } catch (error) {
    console.error('Unexpected error in POST /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/user/preferences
 * Reset user preferences to defaults
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required' 
        },
        { status: 400 }
      )
    }

    // Reset to default preferences
    const defaultData = {
      user_id: userId,
      preferred_currency_code: 'USD',
      display_currency_code: 'USD',
      enable_currency_conversion: true,
      conversion_confirmation_required: true,
      preferences: {}
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .upsert(defaultData)
      .select()
      .single()

    if (error) {
      console.error('Error resetting user preferences:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to reset user preferences' 
        },
        { status: 500 }
      )
    }

    // Transform response
    const resetPreferences: UserCurrencyPreferences = {
      id: data.id,
      userId: data.user_id,
      preferredCurrency: data.preferred_currency_code,
      displayCurrency: data.display_currency_code,
      enableCurrencyConversion: data.enable_currency_conversion,
      conversionConfirmationRequired: data.conversion_confirmation_required,
      preferences: data.preferences || {},
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    }

    return NextResponse.json({
      success: true,
      message: 'User preferences reset to defaults',
      preferences: resetPreferences
    })

  } catch (error) {
    console.error('Unexpected error in PUT /api/user/preferences:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
