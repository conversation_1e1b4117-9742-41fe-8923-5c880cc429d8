"use client"

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Currency, CurrencyDisplay, CurrencySelectorProps } from "@/lib/types"
import { CURRENCIES } from "@/lib/data/currencies"
import { formatCurrency } from "@/lib/utils/currency"
import { cn } from "@/lib/utils"

// Enhanced props interface now imported from types

export function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  availableCurrencies,
  disabled = false,
  className,
  showFlags = false,
  showFullName = false
}: CurrencySelectorProps) {
  // Use provided currencies or fall back to legacy currencies
  const currencies = availableCurrencies || [CURRENCIES.SDG, CURRENCIES.EGP]

  // Determine display mode based on number of currencies
  const useDropdown = currencies.length > 4
  if (useDropdown) {
    return (
      <div className={cn("space-y-2", className)}>
        <Select
          value={selectedCurrency}
          onValueChange={onCurrencyChange}
          disabled={disabled}
        >
          <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
            <SelectValue placeholder="Select currency">
              {selectedCurrency && (
                <div className="flex items-center gap-2">
                  <span className="font-bold">
                    {currencies.find(c => c.code === selectedCurrency)?.symbol}
                  </span>
                  <span className="font-medium">{selectedCurrency}</span>
                  {showFullName && (
                    <span className="text-sm text-slate-400">
                      {currencies.find(c => c.code === selectedCurrency)?.name}
                    </span>
                  )}
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {currencies.map((currency) => (
              <SelectItem key={currency.code} value={currency.code}>
                <div className="flex items-center gap-2">
                  <span className="font-bold">{currency.symbol}</span>
                  <span className="font-medium">{currency.code}</span>
                  <span className="text-sm text-slate-400">{currency.name}</span>
                  {currency.arabicName && (
                    <span className="text-xs text-slate-500">{currency.arabicName}</span>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* ## Enhanced currency selector with dynamic currencies */}
      <ToggleGroup
        type="single"
        value={selectedCurrency}
        onValueChange={(value) => {
          if (value) {
            onCurrencyChange(value as Currency)
          }
        }}
        disabled={disabled}
        className={cn(
          "grid gap-3 w-full",
          currencies.length === 2 ? "grid-cols-2" :
          currencies.length === 3 ? "grid-cols-3" :
          "grid-cols-2"
        )}
      >
        {currencies.map((currency) => (
          <ToggleGroupItem
            key={currency.code}
            value={currency.code}
            className={cn(
              "flex flex-col items-center justify-center gap-1 p-3 h-20 rounded-xl border-2 transition-all duration-300",
              "hover:scale-105 hover:shadow-lg min-w-0 flex-1",
              selectedCurrency === currency.code
                ? "bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900 shadow-lg"
                : "bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500",
              currency.isRTL && "text-right"
            )}
            disabled={disabled}
          >
            <div className="text-xl font-bold leading-none">
              {currency.symbol}
            </div>
            <div className="text-xs font-medium text-center leading-tight px-1">
              {currency.arabicName || currency.name}
            </div>
            {showFullName && currency.arabicName && (
              <div className="text-xs opacity-70 text-center leading-tight">
                {currency.name}
              </div>
            )}
          </ToggleGroupItem>
        ))}
      </ToggleGroup>

      {/* Alternative Select-based Currency Selector (commented out) */}
      {/* 
      <Select value={selectedCurrency} onValueChange={onCurrencyChange} disabled={disabled}>
        <SelectTrigger className="w-full bg-slate-700/50 border-slate-600 text-white">
          <SelectValue placeholder="اختر العملة" />
        </SelectTrigger>
        <SelectContent className="bg-slate-800 border-slate-700">
          <SelectItem value="SDG" className="text-white hover:bg-slate-700">
            <div className="flex items-center gap-3">
              <span className="font-bold">{CURRENCIES.SDG.symbol}</span>
              <span>{CURRENCIES.SDG.arabicName}</span>
            </div>
          </SelectItem>
          <SelectItem value="EGP" className="text-white hover:bg-slate-700">
            <div className="flex items-center gap-3">
              <span className="font-bold">{CURRENCIES.EGP.symbol}</span>
              <span>{CURRENCIES.EGP.arabicName}</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
      */}
    </div>
  )
}
