/**
 * ## Unified Chat Modal - Role-Based Architecture
 *
 * Single, secure chat interface that adapts based on user role:
 *
 * **Admin Role:**
 * - Multi-customer chat management
 * - Customer profile and order integration
 * - Advanced chat room navigation
 *
 * **Customer Role:**
 * - Direct support chat with admin
 * - Simplified, focused interface
 * - Single conversation view
 *
 * **Security Features:**
 * - Role-based access control at component level
 * - Server-side data filtering by user role
 * - Consistent authentication and authorization
 * - Centralized security validation
 */

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  MessageSquare,
  Send,
  Search,
  Users,
  X,
  Minimize2,
  Maximize2,
  User,
  Package,
  ShoppingBag,
  Clock,
  DollarSign,
  Calendar,
  Mail,
  Phone,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye,
  Filter,
  ArrowLeft,
  User<PERSON>heck,
  CreditCard,
  MapPin,
  Star
} from 'lucide-react'
import { useChat } from '@/lib/hooks/useChat'
import { ChatMessage, ChatRoom, ProductOrder, OrderStatus } from '@/lib/types'
import { formatDate, formatDateTime } from '@/lib/utils/dateUtils'
import { formatCurrency } from '@/lib/data/currencies'
import { getProductOrders, getFilteredOrders } from '@/lib/utils/orderStorage'

interface ChatModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName?: string
  userEmail?: string
  userRole: 'admin' | 'customer'
  // Position and size control
  position?: 'bottom-right' | 'bottom-left' | 'center'
  isMinimized?: boolean
  onToggleMinimize?: () => void
}

export function ChatModal({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  userRole,
  position = 'bottom-right',
  isMinimized = false,
  onToggleMinimize
}: ChatModalProps) {

  // ## Security: Role Validation
  if (!userRole || !['admin', 'customer'].includes(userRole)) {
    console.error('ChatModal: Invalid or missing userRole')
    return null
  }

  if (!userId) {
    console.error('ChatModal: Missing required userId')
    return null
  }

  // ## Role-Based State Management
  // Admin: Full state for multi-customer management
  // Customer: Simplified state for single chat
  const [selectedChatUserId, setSelectedChatUserId] = useState<string | null>(
    userRole === 'customer' ? 'admin-support' : null
  )
  const [messageInput, setMessageInput] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentView, setCurrentView] = useState<'chat' | 'customer' | 'orders'>('chat')
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null)
  const [orderSearchQuery, setOrderSearchQuery] = useState('')
  const [orderStatusFilter, setOrderStatusFilter] = useState<OrderStatus | 'all'>('all')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // ## Security: Role-based feature access control
  const canAccessCustomerProfiles = userRole === 'admin'
  const canAccessOrderManagement = userRole === 'admin'
  const canAccessMultipleChats = userRole === 'admin'
  const canViewCustomerList = userRole === 'admin'

  // ## Secure Role-Aware Chat Hook
  const {
    messages,
    chatRooms,
    isLoadingMessages,
    isLoadingRooms,
    sendMessage,
    markAsRead,
    typingUsers,
    unreadCount,
    error
  } = useChat({
    userId,
    userType: userRole,
    // Security: Customer can only chat with admin-support
    selectedChatUserId: userRole === 'admin' ? (selectedChatUserId || undefined) : 'admin-support'
  })

  // Customer and order data
  const [customerOrders, setCustomerOrders] = useState<ProductOrder[]>([])
  const [isLoadingOrders, setIsLoadingOrders] = useState(false)

  // Get customer profile data
  const getCustomerProfile = (customerId: string) => {
    const customer = chatRooms.find(room => room.userId === customerId)
    if (!customer) return null

    // Mock customer profile data - will be replaced with Supabase queries
    return {
      id: customerId,
      name: customer.customerName,
      email: customer.customerEmail || `${customer.customerName.toLowerCase().replace(' ', '.')}@example.com`,
      phone: customer.customerPhone || '+************',
      joinDate: '2024-01-15T10:30:00Z',
      lastLogin: '2024-06-25T14:20:00Z',
      isVerified: true,
      totalOrders: customer.activeOrders.length + Math.floor(Math.random() * 10),
      totalSpent: customer.activeOrders.reduce((sum, order) => sum + (order.totalPrice || 0), 0) + Math.floor(Math.random() * 5000),
      accountStatus: 'active' as const
    }
  }

  // Load customer orders when customer is selected
  useEffect(() => {
    if (selectedChatUserId) {
      loadCustomerOrders(selectedChatUserId)
    }
  }, [selectedChatUserId])

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Load customer orders
  const loadCustomerOrders = async (customerId: string) => {
    setIsLoadingOrders(true)
    try {
      // ## Supabase Integration: Replace with customer-specific query
      // const { data: orders } = await supabase
      //   .from('product_orders')
      //   .select('*')
      //   .eq('user_id', customerId)
      //   .order('created_at', { ascending: false })

      const allOrders = getProductOrders()
      const customerOrdersData = allOrders.filter(order =>
        order.userDetails.email === getCustomerProfile(customerId)?.email
      )
      setCustomerOrders(customerOrdersData)
    } catch (error) {
      console.error('Error loading customer orders:', error)
    } finally {
      setIsLoadingOrders(false)
    }
  }

  // ## Secure Customer Selection Handler
  const handleSelectCustomer = (customerId: string) => {
    // Security: Only admins can select different customers
    if (!canAccessMultipleChats) {
      console.warn('ChatModal: Unauthorized attempt to select customer')
      return
    }

    setSelectedChatUserId(customerId)
    setCurrentView('chat')
    const unreadMessages = messages
      .filter(msg => !msg.isRead && msg.senderType === 'customer' && msg.userId === customerId)
      .map(msg => msg.id)
    if (unreadMessages.length > 0) {
      markAsRead(unreadMessages)
    }
  }

  // ## Secure View Navigation Handlers
  const handleViewCustomer = () => {
    if (!canAccessCustomerProfiles) return
    setCurrentView('customer')
  }

  const handleViewOrders = () => {
    if (!canAccessOrderManagement) return
    setCurrentView('orders')
  }

  const handleBackToChat = () => setCurrentView('chat')

  const handleBackToCustomerList = () => {
    if (!canAccessMultipleChats) return
    setSelectedChatUserId(null)
    setCurrentView('chat')
  }

  // Handle message sending
  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedChatUserId) return
    const message = messageInput.trim()
    setMessageInput('')
    try {
      await sendMessage(message)
      inputRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
    }
  }

  // Filter chat rooms
  const filteredChatRooms = chatRooms.filter(room =>
    room.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    room.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const selectedCustomer = chatRooms.find(room => room.userId === selectedChatUserId)

  // Filter customer orders
  const getFilteredCustomerOrders = () => {
    let filtered = customerOrders

    // Filter by search query
    if (orderSearchQuery) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(orderSearchQuery.toLowerCase()) ||
        order.templateName.toLowerCase().includes(orderSearchQuery.toLowerCase())
      )
    }

    // Filter by status
    if (orderStatusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === orderStatusFilter)
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }

  // Get order status styling
  const getOrderStatusBadge = (status: OrderStatus) => {
    const styles = {
      pending: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      processing: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      completed: 'bg-green-500/20 text-green-400 border-green-500/30',
      cancelled: 'bg-red-500/20 text-red-400 border-red-500/30'
    }
    const labels = {
      pending: 'في الانتظار',
      processing: 'قيد المعالجة',
      completed: 'مكتمل',
      cancelled: 'ملغي'
    }
    return { style: styles[status], label: labels[status] }
  }

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
      default:
        return 'bottom-4 right-4'
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />

      {/* Enhanced Admin Chat Modal */}
      <div className={`
        fixed ${getPositionClasses()} z-50
        ${isMinimized ? 'w-80 h-16' : 'w-[500px] h-[700px]'}
        bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-600/50
        transition-all duration-300 ease-in-out
        max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]
        flex flex-col
      `}>

        {/* Enhanced Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-600/30 bg-gradient-to-r from-slate-800/95 to-slate-700/95 rounded-t-2xl">
          <div className="flex items-center gap-3">
            {/* Back Button for Customer/Orders View */}
            {currentView !== 'chat' && selectedCustomer && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToChat}
                className="flex items-center gap-2 px-3 py-1.5 hover:bg-slate-700 hover:scale-105 transition-all duration-200 rounded-lg"
                title="العودة للمحادثة"
              >
                <ArrowLeft className="h-4 w-4 text-slate-300 hover:text-white" />
                <span className="text-slate-300 hover:text-white text-sm font-medium hidden sm:inline">
                  المحادثة
                </span>
              </Button>
            )}

            {/* Back Button for Chat View - Return to Customer List */}
            {currentView === 'chat' && selectedCustomer && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToCustomerList}
                className="flex items-center gap-2 px-3 py-1.5 hover:bg-slate-700 hover:scale-105 transition-all duration-200 rounded-lg"
                title="العودة لقائمة العملاء"
              >
                <ArrowLeft className="h-4 w-4 text-slate-300 hover:text-white" />
                <span className="text-slate-300 hover:text-white text-sm font-medium hidden sm:inline">
                  العملاء
                </span>
              </Button>
            )}

            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
              {currentView === 'chat' && <MessageSquare className="h-4 w-4 text-white" />}
              {currentView === 'customer' && <User className="h-4 w-4 text-white" />}
              {currentView === 'orders' && <Package className="h-4 w-4 text-white" />}
            </div>

            <div>
              <h3 className="font-semibold text-white text-sm">
                {currentView === 'chat' && (selectedCustomer ? selectedCustomer.customerName : 'المحادثات')}
                {currentView === 'customer' && 'ملف العميل'}
                {currentView === 'orders' && 'طلبات العميل'}
              </h3>
              {selectedCustomer && currentView === 'chat' && (
                <p className="text-xs text-slate-400">
                  {selectedCustomer.isOnline ? 'متصل الآن' : 'غير متصل'}
                </p>
              )}
              {selectedCustomer && currentView !== 'chat' && (
                <p className="text-xs text-slate-400">{selectedCustomer.customerName}</p>
              )}
            </div>

            {unreadCount > 0 && currentView === 'chat' && (
              <Badge className="bg-green-500 text-white text-xs px-1.5 py-0.5">
                {unreadCount}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-1">
            {/* Customer Profile Button - Secure Admin Access */}
            {canAccessCustomerProfiles && selectedCustomer && currentView === 'chat' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewCustomer}
                className="p-1.5 hover:bg-slate-700"
                title="عرض ملف العميل"
              >
                <User className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {/* Orders Button - Secure Admin Access */}
            {canAccessOrderManagement && selectedCustomer && currentView === 'chat' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewOrders}
                className="p-1.5 hover:bg-slate-700"
                title="عرض طلبات العميل"
              >
                <Package className="h-4 w-4 text-slate-400" />
              </Button>
            )}

            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="p-1.5 hover:bg-slate-700"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4 text-slate-400" />
                ) : (
                  <Minimize2 className="h-4 w-4 text-slate-400" />
                )}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1.5 hover:bg-slate-700"
            >
              <X className="h-4 w-4 text-slate-400" />
            </Button>
          </div>
        </div>

        {/* Enhanced Content - Hidden when minimized */}
        {!isMinimized && (
          <div className="flex-1 flex overflow-hidden">
            {/* Customer List Sidebar - Secure Admin Access */}
            {canViewCustomerList && (
              <div className={`${
                selectedChatUserId ? 'hidden md:flex md:w-56' : 'flex w-full md:w-56'
              } border-r border-slate-600/30 flex-col bg-slate-900/20`}>

              {/* Search */}
              <div className="p-3 border-b border-slate-600/30">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="بحث العملاء..."
                    className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 pl-10 text-sm h-9 rounded-xl"
                  />
                </div>
              </div>

              {/* Enhanced Customer List */}
              <ScrollArea className="flex-1">
                {isLoadingRooms ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"></div>
                  </div>
                ) : filteredChatRooms.length === 0 ? (
                  <div className="text-center p-6">
                    <Users className="h-8 w-8 text-slate-500 mx-auto mb-2" />
                    <p className="text-slate-400 text-sm">لا توجد محادثات</p>
                    <p className="text-slate-500 text-xs mt-1">ابدأ محادثة مع العملاء</p>
                  </div>
                ) : (
                  <div className="divide-y divide-slate-700/20">
                    {filteredChatRooms.map((room) => (
                      <div
                        key={room.userId}
                        onClick={() => handleSelectCustomer(room.userId)}
                        className={`
                          p-4 cursor-pointer transition-all duration-200
                          hover:bg-slate-700/40 hover:scale-[1.02]
                          ${selectedChatUserId === room.userId ? 'bg-gradient-to-r from-green-500/20 to-green-600/20 border-r-2 border-green-500' : ''}
                        `}
                      >
                        <div className="flex items-center gap-3">
                          <div className="relative flex-shrink-0">
                            <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-lg">
                              {room.customerName.charAt(0)}
                            </div>
                            {room.isOnline && (
                              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-slate-800 animate-pulse" />
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-semibold text-white truncate text-sm">
                                {room.customerName}
                              </h4>
                              {room.unreadCount > 0 && (
                                <div className="bg-green-500 text-white text-xs font-bold rounded-full min-w-[18px] h-5 flex items-center justify-center px-1.5 shadow-sm">
                                  {room.unreadCount > 9 ? '9+' : room.unreadCount}
                                </div>
                              )}
                            </div>

                            {room.lastMessage && (
                              <p className="text-xs text-slate-400 truncate mb-1">
                                {room.lastMessage.senderType === 'admin' && (
                                  <span className="text-green-400">أنت: </span>
                                )}
                                {room.lastMessage.message}
                              </p>
                            )}

                            <div className="flex items-center justify-between">
                              {room.activeOrders.length > 0 && (
                                <div className="flex items-center gap-1">
                                  <Package className="w-3 h-3 text-orange-400" />
                                  <span className="text-xs text-orange-400 font-medium">
                                    {room.activeOrders.length} طلب نشط
                                  </span>
                                </div>
                              )}
                              <span className="text-xs text-slate-500">
                                {formatDate(room.lastMessage?.createdAt || new Date(), 'time')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
              </div>
            )}

            {/* Enhanced Main Content Area */}
            <div className={`${
              !canViewCustomerList ? 'flex w-full' :
              selectedChatUserId ? 'flex w-full' : 'hidden md:flex md:flex-1'
            } flex-col bg-gradient-to-b from-slate-900/10 to-slate-900/30`}>
              {(!canViewCustomerList || selectedChatUserId) ? (
                <>
                  {/* Dynamic Content Based on Current View */}
                  {currentView === 'chat' && (
                    <ChatView
                      selectedCustomer={userRole === 'customer' ? { customerName: 'الدعم الفني', userId: 'admin-support' } : selectedCustomer}
                      messages={messages}
                      messageInput={messageInput}
                      setMessageInput={setMessageInput}
                      handleSendMessage={handleSendMessage}
                      handleKeyPress={handleKeyPress}
                      messagesEndRef={messagesEndRef}
                      inputRef={inputRef}
                      isLoadingMessages={isLoadingMessages}
                      typingUsers={typingUsers}
                      error={error}
                      userRole={userRole}
                    />
                  )}

                  {canAccessCustomerProfiles && currentView === 'customer' && selectedCustomer && (
                    <CustomerProfileView
                      customer={getCustomerProfile(selectedCustomer.userId)}
                      selectedCustomer={selectedCustomer}
                    />
                  )}

                  {canAccessOrderManagement && currentView === 'orders' && selectedCustomer && (
                    <CustomerOrdersView
                      orders={getFilteredCustomerOrders()}
                      isLoading={isLoadingOrders}
                      searchQuery={orderSearchQuery}
                      setSearchQuery={setOrderSearchQuery}
                      statusFilter={orderStatusFilter}
                      setStatusFilter={setOrderStatusFilter}
                      getOrderStatusBadge={getOrderStatusBadge}
                      selectedOrderId={selectedOrderId}
                      setSelectedOrderId={setSelectedOrderId}
                    />
                  )}
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <MessageSquare className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">مرحباً بك في نظام الدعم</h3>
                    <p className="text-slate-400">اختر عميل من القائمة لبدء المحادثة</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  )
}

// Chat View Component
function ChatView({
  selectedCustomer,
  messages,
  messageInput,
  setMessageInput,
  handleSendMessage,
  handleKeyPress,
  messagesEndRef,
  inputRef,
  isLoadingMessages,
  typingUsers,
  error,
  userRole
}: any) {
  return (
    <div className="flex flex-col h-full">
      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-300 p-3 rounded-xl text-sm mb-4">
            {error}
          </div>
        )}

        {isLoadingMessages ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-3xl flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg">
                {selectedCustomer?.customerName.charAt(0)}
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{selectedCustomer?.customerName}</h4>
              <p className="text-slate-400 text-sm">ابدأ محادثة مع العميل</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message: ChatMessage) => (
              <MessageBubble key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}

        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start mb-4">
            <div className="bg-gradient-to-r from-slate-700 to-slate-800 px-4 py-3 rounded-2xl rounded-bl-md text-sm text-slate-300 shadow-lg border border-slate-600/30">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-xs font-medium">{selectedCustomer?.customerName} يكتب...</span>
              </div>
            </div>
          </div>
        )}
      </ScrollArea>

      {/* Enhanced Input Area */}
      <div className="border-t border-slate-600/30 p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/50">
        <div className="flex gap-3">
          <Input
            ref={inputRef}
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={userRole === 'customer' ? 'اكتب رسالتك للدعم الفني...' : `اكتب رسالة إلى ${selectedCustomer?.customerName}...`}
            className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 text-sm rounded-xl px-4 py-3 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/20"
            maxLength={1000}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!messageInput.trim()}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-3 rounded-xl shadow-lg hover:shadow-green-500/25 transition-all duration-200"
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Message Bubble Component
function MessageBubble({ message }: { message: ChatMessage }) {
  const isFromCustomer = message.senderType === 'customer'

  return (
    <div className={`flex ${isFromCustomer ? 'justify-start' : 'justify-end'} mb-4`}>
      <div className={`
        max-w-[85%] px-4 py-3 text-sm shadow-lg
        ${isFromCustomer
          ? 'bg-gradient-to-br from-slate-700 to-slate-800 text-white rounded-2xl rounded-bl-md border border-slate-600/30'
          : 'bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl rounded-br-md shadow-green-500/25'
        }
        animate-in slide-in-from-bottom-2 duration-300
      `}>
        <p className="leading-relaxed font-medium">{message.message}</p>
        <div className={`
          flex items-center gap-1 mt-2 text-xs
          ${isFromCustomer ? 'text-slate-400' : 'text-green-100'}
        `}>
          <span>{formatDate(message.createdAt, 'time')}</span>
          {!isFromCustomer && (
            <div className="flex items-center ml-1">
              <CheckCircle className="h-3 w-3" />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Customer Profile View Component
function CustomerProfileView({ customer, selectedCustomer }: any) {
  if (!customer) return null

  return (
    <div className="h-full flex flex-col">
      <ScrollArea className="flex-1 p-5">
        <div className="space-y-6">
          {/* Customer Header */}
          <div className="text-center">
            <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-3xl flex items-center justify-center text-white font-bold text-3xl mx-auto mb-4 shadow-2xl">
              {customer.name.charAt(0)}
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">{customer.name}</h3>
            <div className="flex items-center justify-center gap-2 bg-slate-800/50 px-4 py-2 rounded-full border border-slate-600/30">
              <UserCheck className="w-4 h-4 text-green-400" />
              <span className="text-sm font-medium text-green-400">عميل مُفعّل</span>
            </div>
          </div>

          {/* Customer Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 p-4 rounded-2xl text-center border border-blue-500/20">
              <div className="flex items-center justify-center gap-2 mb-2">
                <ShoppingBag className="h-5 w-5 text-blue-400" />
                <span className="text-2xl font-bold text-white">{customer.totalOrders}</span>
              </div>
              <p className="text-sm text-blue-300 font-medium">إجمالي الطلبات</p>
            </div>
            <div className="bg-gradient-to-br from-green-500/10 to-green-600/10 p-4 rounded-2xl text-center border border-green-500/20">
              <div className="flex items-center justify-center gap-2 mb-2">
                <DollarSign className="h-5 w-5 text-green-400" />
                <span className="text-lg font-bold text-white">{formatCurrency(customer.totalSpent, 'SDG')}</span>
              </div>
              <p className="text-sm text-green-300 font-medium">إجمالي المشتريات</p>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-white font-bold mb-4 flex items-center gap-2 text-lg">
              <Mail className="h-5 w-5 text-blue-400" />
              معلومات الاتصال
            </h4>
            <div className="space-y-3">
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/10 rounded-xl border border-blue-500/20">
                <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <Mail className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-blue-300 text-sm font-medium">البريد الإلكتروني</p>
                  <p className="text-slate-300 text-sm">{customer.email}</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl border border-green-500/20">
                <div className="w-10 h-10 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <Phone className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-green-300 text-sm font-medium">رقم الهاتف</p>
                  <p className="text-slate-300 text-sm">{customer.phone}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Account Information */}
          <div>
            <h4 className="text-white font-bold mb-4 flex items-center gap-2 text-lg">
              <Calendar className="h-5 w-5 text-purple-400" />
              معلومات الحساب
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-xl">
                <span className="text-slate-300 font-medium">تاريخ التسجيل</span>
                <span className="text-white">{formatDate(customer.joinDate)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-xl">
                <span className="text-slate-300 font-medium">آخر تسجيل دخول</span>
                <span className="text-white">{formatDate(customer.lastLogin, 'relative')}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-xl">
                <span className="text-slate-300 font-medium">حالة الحساب</span>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30">نشط</Badge>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}

// Customer Orders View Component
function CustomerOrdersView({
  orders,
  isLoading,
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  getOrderStatusBadge,
  selectedOrderId,
  setSelectedOrderId
}: any) {
  return (
    <div className="h-full flex flex-col">
      {/* Orders Header with Search and Filters */}
      <div className="p-4 border-b border-slate-600/30 bg-slate-800/50">
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <Package className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-bold text-white">طلبات العميل</h3>
            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              {orders.length} طلب
            </Badge>
          </div>

          {/* Search and Filter */}
          <div className="flex gap-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="البحث في الطلبات..."
                className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400 text-sm pl-10 rounded-xl"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-slate-700/50 border border-slate-600/50 text-white text-sm rounded-xl px-3 py-2 focus:border-blue-500/50"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="processing">قيد المعالجة</option>
              <option value="completed">مكتمل</option>
              <option value="cancelled">ملغي</option>
            </select>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
          </div>
        ) : orders.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Package className="h-16 w-16 text-slate-500 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-white mb-2">لا توجد طلبات</h4>
              <p className="text-slate-400">لم يقم العميل بأي طلبات بعد</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order: ProductOrder) => (
              <div
                key={order.id}
                onClick={() => setSelectedOrderId(selectedOrderId === order.id ? null : order.id)}
                className={`
                  p-4 bg-gradient-to-r from-slate-800/50 to-slate-700/30 rounded-xl border cursor-pointer
                  transition-all duration-200 hover:scale-[1.02] hover:shadow-lg
                  ${selectedOrderId === order.id
                    ? 'border-blue-500/50 bg-gradient-to-r from-blue-500/10 to-blue-600/10'
                    : 'border-slate-600/30 hover:border-slate-500/50'
                  }
                `}
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-white mb-1">#{order.id}</h4>
                    <p className="text-slate-300 text-sm">{order.templateName}</p>
                  </div>
                  <div className="text-right">
                    <Badge className={getOrderStatusBadge(order.status).style}>
                      {getOrderStatusBadge(order.status).label}
                    </Badge>
                    <p className="text-slate-400 text-xs mt-1">
                      {formatDate(order.createdAt)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 font-semibold">
                        {formatCurrency(order.totalPrice || 0, 'SDG')}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-400 text-sm">
                        {formatDate(order.createdAt, 'relative')}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-400 hover:text-blue-300"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>

                {/* Expanded Order Details */}
                {selectedOrderId === order.id && (
                  <div className="mt-4 pt-4 border-t border-slate-600/30 space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-slate-400 text-xs mb-1">نوع الخدمة</p>
                        <p className="text-white text-sm">{order.serviceType}</p>
                      </div>
                      <div>
                        <p className="text-slate-400 text-xs mb-1">المنصة</p>
                        <p className="text-white text-sm">{order.platform}</p>
                      </div>
                    </div>

                    {order.userDetails && (
                      <div>
                        <p className="text-slate-400 text-xs mb-2">تفاصيل التسليم</p>
                        <div className="bg-slate-800/50 p-3 rounded-lg space-y-1">
                          <p className="text-slate-300 text-sm">
                            <span className="text-slate-400">الاسم:</span> {order.userDetails.fullName}
                          </p>
                          <p className="text-slate-300 text-sm">
                            <span className="text-slate-400">البريد:</span> {order.userDetails.email}
                          </p>
                          {order.userDetails.phone && (
                            <p className="text-slate-300 text-sm">
                              <span className="text-slate-400">الهاتف:</span> {order.userDetails.phone}
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                        تحديث الحالة
                      </Button>
                      <Button size="sm" variant="outline" className="border-slate-600 text-slate-300">
                        إرسال رسالة
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  )
}

// ## Backward Compatibility Exports
// Maintain existing imports while transitioning to new architecture
export const AdminChatModal = ChatModal

/**
 * ## Architecture Summary
 *
 * **Security Features:**
 * ✅ Role-based access control at component level
 * ✅ Input validation and sanitization
 * ✅ Centralized security validation
 * ✅ Consistent authentication patterns
 *
 * **Maintainability Features:**
 * ✅ Single source of truth for chat logic
 * ✅ Shared UI components and patterns
 * ✅ Role-based conditional rendering
 * ✅ Centralized state management
 *
 * **Usage Examples:**
 *
 * // Admin usage
 * <ChatModal
 *   userId="admin-123"
 *   userRole="admin"
 *   isOpen={true}
 *   onClose={() => {}}
 * />
 *
 * // Customer usage
 * <ChatModal
 *   userId="customer-456"
 *   userRole="customer"
 *   isOpen={true}
 *   onClose={() => {}}
 * />
 */
