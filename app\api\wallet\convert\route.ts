import { NextRequest, NextResponse } from 'next/server'
import {
  Cur<PERSON>cy,
  CurrencyConversionRequest,
  CurrencyConversionResponse,
  Transaction
} from '@/lib/types'

// Mock exchange rates for conversion
const mockExchangeRates: Record<string, number> = {
  'USD': 1,
  'SDG': 450.00,
  'EGP': 30.80
}

/**
 * POST /api/wallet/convert
 * Convert wallet balance between currencies
 */
export async function POST(request: NextRequest) {
  try {
    const body: CurrencyConversionRequest = await request.json()

    // Validate request
    const validation = validateConversionRequest(body)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid conversion request',
          message: validation.errors.join(', ')
        },
        { status: 400 }
      )
    }

    const response = await performCurrencyConversion(body)
    
    return NextResponse.json(response, {
      status: response.success ? 200 : 400
    })

  } catch (error) {
    console.error('Error in currency conversion:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to convert currency',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/wallet/convert/preview
 * Preview currency conversion without executing
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fromCurrency = searchParams.get('from') as Currency
    const toCurrency = searchParams.get('to') as Currency
    const amount = parseFloat(searchParams.get('amount') || '0')

    if (!fromCurrency || !toCurrency || amount <= 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid parameters',
          message: 'from, to, and amount parameters are required'
        },
        { status: 400 }
      )
    }

    const preview = await getConversionPreview(fromCurrency, toCurrency, amount)
    
    return NextResponse.json({
      success: true,
      preview
    })

  } catch (error) {
    console.error('Error getting conversion preview:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get conversion preview',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper functions

function validateConversionRequest(request: CurrencyConversionRequest): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!request.userId) {
    errors.push('User ID is required')
  }

  if (!request.fromCurrency) {
    errors.push('From currency is required')
  }

  if (!request.toCurrency) {
    errors.push('To currency is required')
  }

  if (request.fromCurrency === request.toCurrency) {
    errors.push('From and to currencies must be different')
  }

  if (!request.amount || request.amount <= 0) {
    errors.push('Amount must be positive')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

async function getConversionPreview(
  fromCurrency: Currency, 
  toCurrency: Currency, 
  amount: number
) {
  // Get current exchange rate
  const { data: exchangeRate, error } = await supabase
    .rpc('get_exchange_rate', {
      p_from_currency: fromCurrency,
      p_to_currency: toCurrency
    })

  if (error || !exchangeRate) {
    throw new Error(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`)
  }

  const convertedAmount = amount * exchangeRate
  const conversionFee = convertedAmount * 0.01 // 1% fee - configurable
  const totalReceived = convertedAmount - conversionFee

  return {
    originalAmount: amount,
    originalCurrency: fromCurrency,
    convertedAmount,
    targetCurrency: toCurrency,
    exchangeRate,
    conversionFee,
    totalReceived,
    timestamp: new Date()
  }
}

async function performCurrencyConversion(request: CurrencyConversionRequest): Promise<CurrencyConversionResponse> {
  try {
    // Use Supabase RPC function for atomic conversion
    const { data, error } = await supabase
      .rpc('convert_wallet_balance', {
        p_user_id: request.userId,
        p_from_currency: request.fromCurrency,
        p_to_currency: request.toCurrency,
        p_amount: request.amount
      })

    if (error) {
      console.error('Error in wallet conversion RPC:', error)
      return {
        success: false,
        message: 'Conversion failed',
        error: error.message
      }
    }

    if (!data.success) {
      return {
        success: false,
        message: 'Conversion failed',
        error: 'Insufficient balance or invalid conversion'
      }
    }

    // Transform the response
    const conversion = {
      originalAmount: data.original_amount,
      originalCurrency: data.original_currency,
      convertedAmount: data.converted_amount,
      targetCurrency: data.target_currency,
      exchangeRate: data.exchange_rate,
      conversionFee: data.conversion_fee,
      timestamp: new Date()
    }

    // Get the transaction details
    const { data: transactionData } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('id', data.transaction_id)
      .single()

    let transaction: Transaction | undefined
    if (transactionData) {
      transaction = {
        id: transactionData.id,
        userId: transactionData.user_id,
        walletId: transactionData.wallet_id,
        type: transactionData.transaction_type,
        amount: transactionData.amount,
        currency: transactionData.currency_code,
        originalAmount: transactionData.original_amount,
        originalCurrency: transactionData.original_currency_code,
        exchangeRate: transactionData.exchange_rate,
        conversionFee: transactionData.conversion_fee,
        description: transactionData.description,
        referenceNumber: transactionData.reference_number,
        status: transactionData.status,
        date: new Date(transactionData.created_at),
        processedAt: transactionData.processed_at ? new Date(transactionData.processed_at) : undefined
      }
    }

    return {
      success: true,
      message: 'Currency conversion completed successfully',
      conversion,
      transaction
    }

  } catch (error) {
    console.error('Error in performCurrencyConversion:', error)
    return {
      success: false,
      message: 'Conversion failed due to internal error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * GET /api/wallet/convert/history
 * Get conversion history for user
 */
export async function GET_HISTORY(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User ID is required'
        },
        { status: 400 }
      )
    }

    const { data: conversions, error } = await supabase
      .from('wallet_transactions')
      .select(`
        id,
        amount,
        currency_code,
        original_amount,
        original_currency_code,
        exchange_rate,
        conversion_fee,
        description,
        created_at,
        status
      `)
      .eq('user_id', userId)
      .eq('transaction_type', 'currency_conversion')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching conversion history:', error)
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to fetch conversion history'
        },
        { status: 500 }
      )
    }

    const transformedConversions = conversions?.map(conv => ({
      id: conv.id,
      originalAmount: conv.original_amount,
      originalCurrency: conv.original_currency_code,
      convertedAmount: conv.amount,
      targetCurrency: conv.currency_code,
      exchangeRate: conv.exchange_rate,
      conversionFee: conv.conversion_fee,
      timestamp: new Date(conv.created_at),
      status: conv.status
    })) || []

    return NextResponse.json({
      success: true,
      conversions: transformedConversions,
      pagination: {
        limit,
        offset,
        total: transformedConversions.length
      }
    })

  } catch (error) {
    console.error('Error getting conversion history:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get conversion history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
