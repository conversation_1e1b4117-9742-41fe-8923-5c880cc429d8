import { Currency, CurrencyInfo, CurrencyDisplay, LegacyCurrency, LegacyCurrencyInfo } from "@/lib/types"
import { formatCurrency as formatCurrencyNew, getCurrencyDisplayInfo } from "@/lib/utils/currency"

// =====================================================
// LEGACY CURRENCY SUPPORT (BACKWARD COMPATIBILITY)
// =====================================================

// Legacy hardcoded currencies - kept for backward compatibility
export const LEGACY_CURRENCIES: Record<LegacyCurrency, LegacyCurrencyInfo> = {
  SDG: {
    code: "SDG",
    name: "Sudanese Pound",
    symbol: "ج.س.",
    arabicName: "الجنيه السوداني"
  },
  EGP: {
    code: "EGP",
    name: "Egyptian Pound",
    symbol: "ج.م.",
    arabicName: "الجنيه المصري"
  }
}

// Backward compatibility - will be replaced with database-driven currencies
export const CURRENCIES: Record<string, CurrencyDisplay> = {
  SDG: {
    code: "SDG",
    name: "Sudanese Pound",
    symbol: "ج.س.",
    arabicName: "الجنيه السوداني",
    isRTL: true
  },
  EGP: {
    code: "EGP",
    name: "Egyptian Pound",
    symbol: "ج.م.",
    arabicName: "الجنيه المصري",
    isRTL: true
  },
  USD: {
    code: "USD",
    name: "US Dollar",
    symbol: "$",
    arabicName: "الدولار الأمريكي",
    isRTL: false
  }
}

export const DEFAULT_CURRENCY: Currency = "USD"

// =====================================================
// ENHANCED CURRENCY FUNCTIONS
// =====================================================

/**
 * Format currency amount (enhanced version)
 * @deprecated Use formatCurrency from lib/utils/currency.ts instead
 */
export function formatCurrency(amount: number, currency: Currency): string {
  // Use the new enhanced formatting function
  return formatCurrencyNew(amount, currency)
}

/**
 * Get currency info (legacy support)
 * @deprecated Use getCurrencyDisplayInfo from lib/utils/currency.ts instead
 */
export function getCurrencyInfo(currency: Currency): CurrencyDisplay {
  return getCurrencyDisplayInfo(currency)
}

/**
 * Get available currencies for current client
 * TODO: Replace with database lookup in production
 */
export async function getAvailableCurrencies(): Promise<CurrencyDisplay[]> {
  // In production, this would fetch from Supabase
  return Object.values(CURRENCIES)
}

/**
 * Get enabled currencies based on client settings
 * TODO: Replace with client configuration lookup
 */
export async function getEnabledCurrencies(): Promise<Currency[]> {
  // In production, this would fetch from client_currency_settings table
  // USD is always first as the default currency
  return ["USD", "SDG", "EGP"]
}

/**
 * Get primary currency for revenue reporting
 * TODO: Replace with client configuration lookup
 */
export async function getPrimaryCurrency(): Promise<Currency> {
  // In production, this would fetch from client_currency_settings table
  return "USD"
}

/**
 * Check if multi-currency is enabled
 * TODO: Replace with client configuration lookup
 */
export async function isMultiCurrencyEnabled(): Promise<boolean> {
  // In production, this would fetch from client_currency_settings table
  return true
}

/**
 * Check if currency conversion is enabled
 * TODO: Replace with client configuration lookup
 */
export async function isCurrencyConversionEnabled(): Promise<boolean> {
  // In production, this would fetch from client_currency_settings table
  return true
}
