"use client"

import React, { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  ShoppingCart,
  Star,
  Plus,
  Minus,
  AlertCircle,
  CheckCircle,
  Calculator,
  Clock
} from "lucide-react"
import { ProductTemplate, DynamicField, Currency } from "@/lib/types"
import { validateField, validateForm } from "@/lib/validation"
import { ImageFieldComponent } from "./ImageFieldComponent"
import {
  createProductOrder,
  calculateOrderPricing,
  validateOrderD<PERSON>,
  determineProcessingType
} from "@/lib/utils/orderUtils"
import { saveProductOrder } from "@/lib/utils/orderStorage"

interface InteractiveProductFormProps {
  template: ProductTemplate
  onSubmit?: (formData: Record<string, any>) => void
  currency?: Currency
  showPricing?: boolean
}

export function InteractiveProductForm({
  template,
  onSubmit,
  currency = "SDG",
  showPricing = true
}: InteractiveProductFormProps) {
  const router = useRouter()
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pricing, setPricing] = useState<any>(null)

  const handleFieldChange = (fieldName: string, value: any, field: DynamicField) => {
    // Update form data
    const newFormData = {
      ...formData,
      [fieldName]: value
    }
    setFormData(newFormData)

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }))

    // Validate field
    const validation = validateField(field, value)
    setErrors(prev => ({
      ...prev,
      [fieldName]: validation.isValid ? "" : (validation.error || "")
    }))

    // Update pricing if pricing is enabled
    if (showPricing) {
      try {
        const newPricing = calculateOrderPricing(template, newFormData, currency)
        setPricing(newPricing)
      } catch (error) {
        console.error("Error calculating pricing:", error)
      }
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // Validate entire form
      const validation = validateForm(template.fields, formData)

      if (!validation.isValid) {
        setErrors(validation.errors)
        // Mark all fields as touched to show errors
        const allTouched = template.fields.reduce((acc, field) => ({
          ...acc,
          [field.name]: true
        }), {})
        setTouched(allTouched)
        return
      }

      // Validate order data
      const orderValidation = validateOrderData(template, formData)
      if (!orderValidation.isValid) {
        alert(`خطأ في البيانات: ${orderValidation.errors.join(", ")}`)
        return
      }

      // For now, we'll use mock user details
      // ## Supabase Integration: Get user details from auth.user()
      const mockUserDetails = {
        fullName: "مستخدم تجريبي",
        email: "<EMAIL>",
        phone: "+249123456789"
      }

      // Create product order
      const order = createProductOrder(template, formData, mockUserDetails, currency)

      // Save order to storage
      // ## Supabase Integration: This will be replaced with database insert
      saveProductOrder(order)

      // Call onSubmit callback if provided
      onSubmit?.(formData)

      // Navigate to order confirmation or checkout
      // For now, show success message and redirect to orders page
      alert(`تم إنشاء الطلب بنجاح! رقم الطلب: ${order.id}`)

      // Navigate to orders page to show the created order
      router.push(`/orders?highlight=${order.id}`)

    } catch (error) {
      console.error("Error creating order:", error)
      alert("حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (field: DynamicField) => {
    if (!field.visible) return null

    const fieldValue = formData[field.name]
    const fieldError = errors[field.name]
    const isFieldTouched = touched[field.name]
    const showError = isFieldTouched && fieldError

    const baseClasses = `bg-slate-700/50 border-slate-600 text-white ${
      showError ? "border-red-500" : ""
    }`

    switch (field.type) {
      case "heading":
        const HeadingTag = `h${field.level || 2}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag 
            key={field.id}
            className={`font-bold text-white mb-4 ${
              field.level === 1 ? "text-3xl" : 
              field.level === 2 ? "text-2xl" : 
              field.level === 3 ? "text-xl" : 
              field.level === 4 ? "text-lg" : 
              field.level === 5 ? "text-base" : "text-sm"
            } ${
              field.alignment === "center" ? "text-center" : 
              field.alignment === "right" ? "text-right" : "text-left"
            }`}
            style={{ color: field.color }}
          >
            {field.label}
          </HeadingTag>
        )

      case "divider":
        return (
          <div key={field.id} className="my-6">
            <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent" />
          </div>
        )

      case "text":
      case "email":
      case "number":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Input
              type={field.type === "number" ? "number" : field.type === "email" ? "email" : "text"}
              value={fieldValue || ""}
              onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
              placeholder={field.placeholder}
              className={baseClasses}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "textarea":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Textarea
              value={fieldValue || ""}
              onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
              placeholder={field.placeholder}
              className={baseClasses}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "select":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <Select
              value={fieldValue || ""}
              onValueChange={(value) => handleFieldChange(field.name, value, field)}
            >
              <SelectTrigger className={baseClasses}>
                <SelectValue placeholder={field.placeholder || "اختر..."} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.id} value={option.value}>
                    <div className="flex items-center justify-between w-full">
                      <span>{option.label}</span>
                      {option.price && option.price > 0 && (
                        <span className="text-green-400 text-sm mr-2">+{option.price} ج.س.</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "radio":
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-slate-300">
              {field.label}
              {field.required && <span className="text-red-400 mr-1">*</span>}
            </Label>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <label key={option.id} className="flex items-center gap-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name={field.name}
                    value={option.value}
                    checked={fieldValue === option.value}
                    onChange={(e) => handleFieldChange(field.name, e.target.value, field)}
                    className="text-blue-500 border-slate-600 bg-slate-700"
                  />
                  <span className="text-white">{option.label}</span>
                  {option.price && option.price > 0 && (
                    <span className="text-green-400 text-sm">+{option.price} ج.س.</span>
                  )}
                </label>
              ))}
            </div>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
            {field.description && (
              <p className="text-sm text-slate-400">{field.description}</p>
            )}
          </div>
        )

      case "checkbox":
        return (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center space-x-2 space-x-reverse">
              <input
                type="checkbox"
                checked={fieldValue || false}
                onChange={(e) => handleFieldChange(field.name, e.target.checked, field)}
                className="rounded border-slate-600 bg-slate-700"
              />
              <Label className="text-slate-300">
                {field.label}
                {field.required && <span className="text-red-400 mr-1">*</span>}
              </Label>
            </div>
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
          </div>
        )

      case "image":
        return (
          <div key={field.id} className="space-y-2">
            <ImageFieldComponent
              field={field}
              value={fieldValue}
              onChange={(value) => handleFieldChange(field.name, value, field)}
            />
            {showError && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">{fieldError}</AlertDescription>
              </Alert>
            )}
          </div>
        )

      default:
        return (
          <div key={field.id} className="p-4 bg-slate-700/30 border border-slate-600 rounded-lg">
            <p className="text-slate-400">نوع حقل غير مدعوم: {field.type}</p>
          </div>
        )
    }
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
      <CardHeader>
        <div className="text-center space-y-4">
          <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            {template.name}
          </h1>
          {template.description && (
            <p className="text-slate-300">{template.description}</p>
          )}
          <Badge variant="secondary">{template.category}</Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Render all fields */}
        {template.fields
          .sort((a, b) => a.order - b.order)
          .map(renderField)}

        {/* Pricing Display */}
        {showPricing && pricing && (
          <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
            <div className="flex items-center gap-2 mb-3">
              <Calculator className="h-5 w-5 text-yellow-400" />
              <h3 className="text-white font-medium">تفاصيل السعر</h3>
            </div>

            <div className="space-y-2 text-sm">
              {pricing.basePrice > 0 && (
                <div className="flex justify-between text-slate-300">
                  <span>السعر الأساسي:</span>
                  <span>{pricing.basePrice} {currency}</span>
                </div>
              )}

              {pricing.modifiers.map((modifier, index) => (
                <div key={index} className="flex justify-between text-slate-300">
                  <span>{modifier.fieldLabel}:</span>
                  <span>
                    {modifier.type === "add" ? "+" : ""}
                    {modifier.modifier} {currency}
                  </span>
                </div>
              ))}

              {pricing.quantity > 1 && (
                <div className="flex justify-between text-slate-300">
                  <span>الكمية:</span>
                  <span>× {pricing.quantity}</span>
                </div>
              )}

              <div className="border-t border-slate-600 pt-2 mt-3">
                <div className="flex justify-between text-lg font-bold text-yellow-400">
                  <span>الإجمالي:</span>
                  <span>{pricing.totalPrice} {currency}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Type Indicator */}
        {formData && Object.keys(formData).length > 0 && (
          <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-400" />
              <span className="text-white font-medium">نوع المعالجة:</span>
              <Badge
                variant={determineProcessingType(template, formData) === "instant" ? "default" : "secondary"}
                className="mr-2"
              >
                {determineProcessingType(template, formData) === "instant" ? "فوري" : "يدوي"}
              </Badge>
            </div>
            <p className="text-slate-400 text-sm mt-2">
              {determineProcessingType(template, formData) === "instant"
                ? "سيتم معالجة طلبك تلقائياً خلال دقائق"
                : "سيتم مراجعة طلبك من قبل فريق الدعم خلال 24 ساعة"
              }
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-slate-700">
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:opacity-50"
          >
            <ShoppingCart className="h-4 w-4 ml-2" />
            {isSubmitting ? "جاري الإرسال..." : "إنشاء الطلب"}
          </Button>
          <Button
            variant="outline"
            className="border-slate-600 text-slate-300"
            disabled={isSubmitting}
          >
            <Star className="h-4 w-4 ml-2" />
            إضافة للمفضلة
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
