"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Settings, 
  DollarSign, 
  TrendingUp,
  Globe,
  Shield,
  Bell,
  Database,
  Users,
  BarChart3,
  Loader2,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { CurrencySettingsPage } from "@/components/pages/CurrencySettingsPage"
import { ExchangeRateManager } from "@/components/admin/ExchangeRateManager"
import { RevenueReportDashboard } from "@/components/admin/RevenueReportDashboard"
import { CurrencyIntegrationAudit } from "@/components/admin/CurrencyIntegrationAudit"
import { Currency } from "@/lib/types"
import { cn } from "@/lib/utils"

export function AdminSettingsDashboard() {
  const [activeTab, setActiveTab] = useState("currency")
  const [systemHealth, setSystemHealth] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadSystemHealth()
  }, [])

  const loadSystemHealth = async () => {
    setIsLoading(true)
    try {
      // TODO: Replace with actual system health check API
      const mockHealth = {
        status: 'healthy',
        currencies: { active: 3, total: 5 },
        exchangeRates: { active: 6, stale: 0 },
        lastUpdate: new Date(),
        issues: []
      }
      setSystemHealth(mockHealth)
    } catch (error) {
      console.error('Failed to load system health:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'error': return 'text-red-400'
      default: return 'text-slate-400'
    }
  }

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'error': return <AlertTriangle className="h-4 w-4" />
      default: return <Loader2 className="h-4 w-4 animate-spin" />
    }
  }

  return (
    <div className="text-white p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                System Settings
              </h1>
              <p className="text-slate-400 text-lg">Manage currencies, exchange rates, and system configuration</p>
            </div>
            
            {/* System Health Indicator */}
            {systemHealth && (
              <div className="flex items-center gap-2">
                <div className={cn("flex items-center gap-1", getHealthStatusColor(systemHealth.status))}>
                  {getHealthStatusIcon(systemHealth.status)}
                  <span className="text-sm font-medium capitalize">{systemHealth.status}</span>
                </div>
                <Badge variant="outline" className="text-slate-300 border-slate-600">
                  {systemHealth.currencies.active} Currencies Active
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* System Health Overview */}
        {systemHealth && systemHealth.issues.length > 0 && (
          <Alert className="mb-6 bg-yellow-900/20 border-yellow-700/50">
            <AlertTriangle className="h-4 w-4 text-yellow-400" />
            <AlertDescription className="text-yellow-100">
              <div className="font-medium mb-1">System Issues Detected:</div>
              <ul className="list-disc list-inside text-sm">
                {systemHealth.issues.map((issue: string, index: number) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Settings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7 bg-slate-800/50 border-slate-700">
            <TabsTrigger
              value="currency"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Currencies</span>
            </TabsTrigger>
            <TabsTrigger
              value="rates"
              className="data-[state=active]:bg-green-600 data-[state=active]:text-white"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Exchange Rates</span>
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Analytics</span>
            </TabsTrigger>
            <TabsTrigger
              value="audit"
              className="data-[state=active]:bg-cyan-600 data-[state=active]:text-white"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Integration Audit</span>
            </TabsTrigger>
            <TabsTrigger
              value="system"
              className="data-[state=active]:bg-orange-600 data-[state=active]:text-white"
            >
              <Settings className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">System</span>
            </TabsTrigger>
            <TabsTrigger
              value="security"
              className="data-[state=active]:bg-red-600 data-[state=active]:text-white"
            >
              <Shield className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Security</span>
            </TabsTrigger>
            <TabsTrigger
              value="notifications"
              className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
            >
              <Bell className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Notifications</span>
            </TabsTrigger>
          </TabsList>

          {/* Currency Management Tab */}
          <TabsContent value="currency" className="space-y-6">
            <CurrencySettingsPage />
          </TabsContent>

          {/* Exchange Rates Tab */}
          <TabsContent value="rates" className="space-y-6">
            <ExchangeRateManager />
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <RevenueReportDashboard />
          </TabsContent>

          {/* Integration Audit Tab */}
          <TabsContent value="audit" className="space-y-6">
            <CurrencyIntegrationAudit />
          </TabsContent>

          {/* System Settings Tab */}
          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
                <CardHeader>
                  <CardTitle className="text-xl text-white flex items-center gap-2">
                    <Database className="h-5 w-5 text-blue-400" />
                    Database Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Connection Status</span>
                    <Badge className="bg-green-600 text-white">Connected</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Active Connections</span>
                    <span className="text-white font-medium">12/100</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Last Backup</span>
                    <span className="text-white font-medium">2 hours ago</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
                <CardHeader>
                  <CardTitle className="text-xl text-white flex items-center gap-2">
                    <Users className="h-5 w-5 text-green-400" />
                    User Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Total Users</span>
                    <span className="text-white font-medium">1,247</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Active Today</span>
                    <span className="text-white font-medium">89</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">New This Week</span>
                    <span className="text-white font-medium">23</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <Shield className="h-5 w-5 text-red-400" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Shield className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Security Configuration</h3>
                  <p className="text-slate-400">
                    Advanced security settings and audit logs will be available here.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <Bell className="h-5 w-5 text-indigo-400" />
                  Notification Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Bell className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Notification Management</h3>
                  <p className="text-slate-400">
                    Email, SMS, and push notification settings will be configured here.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
