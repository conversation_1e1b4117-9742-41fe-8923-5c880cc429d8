"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ShoppingBag,
  Search,
  Filter,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  RotateCcw,
  Calendar,
  Package,
  DollarSign,
  User,
  Phone,
  Mail
} from "lucide-react"
import { 
  ProductOrder, 
  OrderStatus, 
  Currency 
} from "@/lib/types"
import {
  getUserOrders,
  getProductOrderById
} from "@/lib/utils/orderStorage"
import { formatCurrency } from "@/lib/data/currencies"
import { formatFieldDataForDisplay } from "@/lib/utils/orderUtils"
import { formatDate, formatDateTime } from "@/lib/utils/dateUtils"
import { initializeMockProductOrders } from "@/lib/data/mockProductOrders"

export function OrdersPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const highlightOrderId = searchParams.get("highlight")
  
  // State management
  const [activeTab, setActiveTab] = useState("orders")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [orders, setOrders] = useState<ProductOrder[]>([])
  const [filteredOrders, setFilteredOrders] = useState<ProductOrder[]>([])
  const [selectedOrder, setSelectedOrder] = useState<ProductOrder | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | OrderStatus>("all")
  const [isLoading, setIsLoading] = useState(true)

  // ## Supabase Integration: Replace with user-specific query
  const loadUserOrders = async () => {
    setIsLoading(true)
    try {
      // Initialize mock data if needed
      initializeMockProductOrders()

      // ## Supabase Integration: Get user ID from auth and filter orders
      // const { data: { user } } = await supabase.auth.getUser()
      // const userOrders = await supabase
      //   .from('product_orders')
      //   .select('*')
      //   .eq('user_id', user.id)
      //   .order('created_at', { ascending: false })

      const userOrders = getUserOrders()
      setOrders(userOrders)
      
      // Highlight specific order if provided
      if (highlightOrderId) {
        const highlightOrder = userOrders.find(o => o.id === highlightOrderId)
        if (highlightOrder) {
          setSelectedOrder(highlightOrder)
        }
      }
    } catch (error) {
      console.error("Error loading user orders:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Load orders on component mount
  useEffect(() => {
    loadUserOrders()
  }, [highlightOrderId])

  // Filter orders based on search and status
  useEffect(() => {
    let filtered = orders

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(order => 
        order.id.toLowerCase().includes(query) ||
        order.templateName.toLowerCase().includes(query) ||
        order.templateCategory.toLowerCase().includes(query)
      )
    }

    setFilteredOrders(filtered)
  }, [orders, statusFilter, searchQuery])

  // Navigation handlers
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  // Order status helpers
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "processing":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "cancelled":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />
      case "processing":
        return <AlertCircle className="h-4 w-4" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "cancelled":
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusLabel = (status: OrderStatus) => {
    const labels: Record<OrderStatus, string> = {
      pending: "في الانتظار",
      processing: "قيد المعالجة",
      completed: "مكتمل",
      failed: "فشل",
      cancelled: "ملغي"
    }
    return labels[status]
  }

  // Get order statistics
  const getOrderStats = () => {
    return {
      total: orders.length,
      pending: orders.filter(o => o.status === "pending").length,
      processing: orders.filter(o => o.status === "processing").length,
      completed: orders.filter(o => o.status === "completed").length,
      failed: orders.filter(o => o.status === "failed").length
    }
  }

  const stats = getOrderStats()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl shadow-lg">
              <ShoppingBag className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-4">
            طلباتي
          </h1>
          <p className="text-slate-300 text-lg">
            تتبع ومراجعة جميع طلباتك السابقة والحالية
          </p>
        </div>

        {/* Order Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-white">{stats.total}</div>
              <div className="text-sm text-slate-400">إجمالي الطلبات</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">{stats.pending}</div>
              <div className="text-sm text-slate-400">في الانتظار</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-400">{stats.processing}</div>
              <div className="text-sm text-slate-400">قيد المعالجة</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{stats.completed}</div>
              <div className="text-sm text-slate-400">مكتمل</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-400">{stats.failed}</div>
              <div className="text-sm text-slate-400">فشل</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="البحث برقم الطلب أو اسم المنتج..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-slate-700/50 border-slate-600 text-white pr-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadUserOrders}
                  className="border-slate-600 text-slate-300"
                >
                  <RefreshCw className="h-4 w-4 ml-2" />
                  تحديث
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Tabs */}
        <Tabs value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
          <TabsList className="grid w-full grid-cols-6 bg-slate-800/50 mb-6">
            <TabsTrigger value="all" className="data-[state=active]:bg-slate-700">
              الكل
            </TabsTrigger>
            <TabsTrigger value="pending" className="data-[state=active]:bg-slate-700">
              في الانتظار
            </TabsTrigger>
            <TabsTrigger value="processing" className="data-[state=active]:bg-slate-700">
              قيد المعالجة
            </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:bg-slate-700">
              مكتمل
            </TabsTrigger>
            <TabsTrigger value="failed" className="data-[state=active]:bg-slate-700">
              فشل
            </TabsTrigger>
            <TabsTrigger value="cancelled" className="data-[state=active]:bg-slate-700">
              ملغي
            </TabsTrigger>
          </TabsList>

          <TabsContent value={statusFilter}>
            {isLoading ? (
              <div className="text-center py-12">
                <RefreshCw className="h-8 w-8 animate-spin text-slate-400 mx-auto mb-4" />
                <p className="text-slate-400">جاري تحميل الطلبات...</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">لا توجد طلبات</p>
                <p className="text-slate-500 mb-6">
                  {statusFilter === "all" 
                    ? "لم تقم بأي طلبات بعد" 
                    : `لا توجد طلبات ${getStatusLabel(statusFilter as OrderStatus)}`
                  }
                </p>
                <Button 
                  onClick={() => router.push("/shop")}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  تصفح المنتجات
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <Card 
                    key={order.id} 
                    className={`bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-colors cursor-pointer ${
                      order.id === highlightOrderId ? "ring-2 ring-blue-500" : ""
                    }`}
                    onClick={() => setSelectedOrder(order)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1">
                          <div className="p-2 bg-slate-700/50 rounded-lg">
                            {getStatusIcon(order.status)}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium text-white truncate">
                                {order.templateName}
                              </h3>
                              <Badge className={getStatusColor(order.status)}>
                                {getStatusLabel(order.status)}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-slate-400">
                              <span className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                {order.id}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(order.createdAt)}
                              </span>
                              <span className="flex items-center gap-1">
                                <DollarSign className="h-3 w-3" />
                                {formatCurrency(order.pricing.totalPrice, order.pricing.currency)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedOrder(order)
                            }}
                            className="text-slate-400 hover:text-white"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {order.status === "completed" && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                // TODO: Implement reorder functionality
                                alert("إعادة الطلب - قيد التطوير")
                              }}
                              className="text-slate-400 hover:text-blue-400"
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Order Details Modal - Placeholder */}
        {selectedOrder && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card className="bg-slate-800 border-slate-700 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  تفاصيل الطلب: {selectedOrder.id}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedOrder(null)}
                    className="text-slate-400 hover:text-white"
                  >
                    ✕
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">المنتج</p>
                    <p className="text-white font-medium">{selectedOrder.templateName}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">الحالة</p>
                    <Badge className={getStatusColor(selectedOrder.status)}>
                      {getStatusLabel(selectedOrder.status)}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">التاريخ</p>
                    <p className="text-white">{formatDateTime(selectedOrder.createdAt)}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">المبلغ</p>
                    <p className="text-white font-medium">
                      {formatCurrency(selectedOrder.pricing.totalPrice, selectedOrder.pricing.currency)}
                    </p>
                  </div>
                </div>
                
                <div className="border-t border-slate-700 pt-4">
                  <p className="text-slate-400 text-sm mb-2">تفاصيل إضافية</p>
                  <p className="text-slate-300">تفاصيل الطلب الكاملة - قيد التطوير</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
