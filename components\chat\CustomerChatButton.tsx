/**
 * ## Customer Chat But<PERSON>
 * Floating support button for customer pages
 * Opens customer chat interface in a modal
 */

'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { MessageCircle, X, Headphones } from 'lucide-react'
import { CustomerChatInterface } from './CustomerChatInterface'
import { useChat } from '@/lib/hooks/useChat'

interface CustomerChatButtonProps {
  userId: string
  userName?: string
  userEmail?: string
  // Position of the floating button
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  // Custom styling
  className?: string
}

export function CustomerChatButton({ 
  userId, 
  userName, 
  userEmail,
  position = 'bottom-right',
  className = ''
}: CustomerChatButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [hasNewMessages, setHasNewMessages] = useState(false)

  // Get unread count from chat hook
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  // Animate button when new messages arrive
  useEffect(() => {
    if (unreadCount > 0 && !isModalOpen) {
      setHasNewMessages(true)
      const timer = setTimeout(() => setHasNewMessages(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [unreadCount, isModalOpen])

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  }

  return (
    <>
      {/* Floating Support Button */}
      <div className={`fixed ${positionClasses[position]} z-50 ${className}`}>
        <div className="relative">
          <Button
            onClick={() => setIsModalOpen(true)}
            className={`
              w-14 h-14 rounded-full shadow-lg transition-all duration-300
              bg-gradient-to-r from-green-500 to-green-600 
              hover:from-green-600 hover:to-green-700
              hover:scale-110 active:scale-95
              ${hasNewMessages ? 'animate-bounce' : ''}
            `}
          >
            <Headphones className="w-6 h-6 text-white" />
          </Button>

          {/* Unread Badge */}
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full animate-pulse">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}

          {/* Pulse Ring Animation */}
          {hasNewMessages && (
            <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-30"></div>
          )}
        </div>

        {/* Tooltip */}
        <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            الدعم الفني
          </div>
        </div>
      </div>

      {/* Chat Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl w-full h-[80vh] p-0 bg-slate-900 border-slate-700">
          <DialogHeader className="p-4 border-b border-slate-700">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-white flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  <Headphones className="w-4 h-4 text-white" />
                </div>
                الدعم الفني
              </DialogTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsModalOpen(false)}
                className="text-slate-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </DialogHeader>
          
          <div className="flex-1 p-4">
            <CustomerChatInterface
              userId={userId}
              userName={userName}
              userEmail={userEmail}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * ## Customer Chat Widget
 * Inline widget version for integration into customer dashboard
 */
interface CustomerChatWidgetProps {
  userId: string
  userName?: string
  userEmail?: string
  className?: string
}

export function CustomerChatWidget({ 
  userId, 
  userName, 
  userEmail,
  className = ''
}: CustomerChatWidgetProps) {
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  return (
    <div className={`bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
            <Headphones className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-white">الدعم الفني</h3>
            <p className="text-sm text-slate-400">نحن هنا لمساعدتك</p>
          </div>
        </div>
        
        {unreadCount > 0 && (
          <Badge className="bg-red-500 text-white">
            {unreadCount} رسالة جديدة
          </Badge>
        )}
      </div>

      <CustomerChatInterface
        userId={userId}
        userName={userName}
        userEmail={userEmail}
      />
    </div>
  )
}

/**
 * ## Customer Chat Notification Bar
 * Compact notification bar for customer pages
 */
interface CustomerChatNotificationProps {
  userId: string
  onOpenChat: () => void
  className?: string
}

export function CustomerChatNotification({ 
  userId, 
  onOpenChat,
  className = ''
}: CustomerChatNotificationProps) {
  const { unreadCount } = useChat({
    userId,
    userType: 'customer'
  })

  if (unreadCount === 0) return null

  return (
    <div className={`bg-green-500/10 border border-green-500/20 rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <MessageCircle className="w-4 h-4 text-white" />
          </div>
          <div>
            <p className="text-sm font-medium text-green-300">
              لديك {unreadCount} رسالة جديدة من فريق الدعم
            </p>
            <p className="text-xs text-green-400">انقر لعرض المحادثة</p>
          </div>
        </div>

        <Button
          onClick={onOpenChat}
          className="bg-green-500 hover:bg-green-600 text-white text-sm px-4 py-2"
        >
          عرض الرسائل
        </Button>
      </div>
    </div>
  )
}

/**
 * ## Usage Examples:
 * 
 * // Floating button (recommended for most customer pages)
 * <CustomerChatButton userId="customer-123" />
 * 
 * // Integrated widget (for customer dashboard)
 * <CustomerChatWidget userId="customer-123" className="col-span-1" />
 * 
 * // Notification bar (for customer header)
 * <CustomerChatNotification 
 *   userId="customer-123" 
 *   onOpenChat={() => setShowChat(true)} 
 * />
 */
