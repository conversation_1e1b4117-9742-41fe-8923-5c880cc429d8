"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Settings, 
  Save, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  DollarSign,
  Globe
} from "lucide-react"
import { 
  Currency, 
  CurrencyDisplay, 
  UserCurrencyPreferences
} from "@/lib/types"
import { formatCurrency, getCurrencyDisplayInfo } from "@/lib/utils/currency"
import { cn } from "@/lib/utils"

interface CurrencyPreferencesProps {
  userId: string
  availableCurrencies: CurrencyDisplay[]
  currentPreferences?: UserCurrencyPreferences
  onPreferencesUpdate?: (preferences: UserCurrencyPreferences) => void
  className?: string
}

export function CurrencyPreferences({
  userId,
  availableCurrencies,
  currentPreferences,
  onPreferencesUpdate,
  className
}: CurrencyPreferencesProps) {
  const [preferences, setPreferences] = useState<Partial<UserCurrencyPreferences>>({
    preferredCurrency: "USD", // Default to USD
    displayCurrency: "USD",
    enableCurrencyConversion: true,
    conversionConfirmationRequired: true
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Load current preferences
  useEffect(() => {
    if (currentPreferences) {
      setPreferences({
        preferredCurrency: currentPreferences.preferredCurrency,
        displayCurrency: currentPreferences.displayCurrency,
        enableCurrencyConversion: currentPreferences.enableCurrencyConversion,
        conversionConfirmationRequired: currentPreferences.conversionConfirmationRequired
      })
    } else {
      loadUserPreferences()
    }
  }, [currentPreferences, userId])

  const loadUserPreferences = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // TODO: Replace with actual API call
      const response = await fetch(`/api/user/preferences?userId=${userId}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.preferences) {
          setPreferences({
            preferredCurrency: data.preferences.preferredCurrency,
            displayCurrency: data.preferences.displayCurrency,
            enableCurrencyConversion: data.preferences.enableCurrencyConversion,
            conversionConfirmationRequired: data.preferences.conversionConfirmationRequired
          })
        }
      }
    } catch (err) {
      console.warn('Failed to load user preferences, using defaults')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSavePreferences = async () => {
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          preferences
        })
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Failed to save preferences')
      }

      setSuccess('Currency preferences saved successfully!')
      
      // Call the callback if provided
      if (onPreferencesUpdate && data.preferences) {
        onPreferencesUpdate(data.preferences)
      }

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save preferences')
    } finally {
      setIsSaving(false)
    }
  }

  const handlePreferenceChange = (key: keyof UserCurrencyPreferences, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const getPreferredCurrencyInfo = () => {
    if (!preferences.preferredCurrency) return null
    return availableCurrencies.find(c => c.code === preferences.preferredCurrency) ||
           getCurrencyDisplayInfo(preferences.preferredCurrency)
  }

  if (isLoading) {
    return (
      <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
        <CardContent className="flex items-center justify-center h-32">
          <Loader2 className="h-6 w-6 animate-spin text-blue-400" />
          <span className="ml-2 text-slate-400">Loading preferences...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("bg-slate-800/50 backdrop-blur-xl border-slate-700/50", className)}>
      <CardHeader>
        <CardTitle className="text-xl text-white flex items-center gap-2">
          <Settings className="h-5 w-5 text-blue-400" />
          Currency Preferences
        </CardTitle>
        <p className="text-slate-400 text-sm">
          Customize your default currency and conversion settings
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Alerts */}
        {error && (
          <Alert className="bg-red-900/20 border-red-700/50">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-100">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-900/20 border-green-700/50">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-100">{success}</AlertDescription>
          </Alert>
        )}

        {/* Preferred Currency */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-blue-400" />
            <Label className="text-slate-300 font-medium">Preferred Currency</Label>
          </div>
          <Select 
            value={preferences.preferredCurrency || "USD"} 
            onValueChange={(value) => handlePreferenceChange('preferredCurrency', value as Currency)}
          >
            <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
              <SelectValue placeholder="Select your preferred currency">
                {preferences.preferredCurrency && (
                  <div className="flex items-center gap-2">
                    <span className="font-bold">
                      {getPreferredCurrencyInfo()?.symbol}
                    </span>
                    <span className="font-medium">{preferences.preferredCurrency}</span>
                    <span className="text-sm text-slate-400">
                      {getPreferredCurrencyInfo()?.name}
                    </span>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {availableCurrencies.map((currency) => (
                <SelectItem key={currency.code} value={currency.code}>
                  <div className="flex items-center gap-2">
                    <span className="font-bold">{currency.symbol}</span>
                    <span className="font-medium">{currency.code}</span>
                    <span className="text-sm text-slate-400">{currency.name}</span>
                    {currency.arabicName && (
                      <span className="text-xs text-slate-500">{currency.arabicName}</span>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-slate-400">
            This will be your default currency for new transactions and wallet operations
          </p>
        </div>

        {/* Display Currency */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-green-400" />
            <Label className="text-slate-300 font-medium">Display Currency</Label>
          </div>
          <Select 
            value={preferences.displayCurrency || "USD"} 
            onValueChange={(value) => handlePreferenceChange('displayCurrency', value as Currency)}
          >
            <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
              <SelectValue placeholder="Select display currency" />
            </SelectTrigger>
            <SelectContent>
              {availableCurrencies.map((currency) => (
                <SelectItem key={currency.code} value={currency.code}>
                  <div className="flex items-center gap-2">
                    <span className="font-bold">{currency.symbol}</span>
                    <span className="font-medium">{currency.code}</span>
                    <span className="text-sm text-slate-400">{currency.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-slate-400">
            Currency used for displaying prices and amounts throughout the app
          </p>
        </div>

        {/* Conversion Settings */}
        <div className="space-y-4 p-4 bg-slate-700/20 rounded-lg border border-slate-600/30">
          <h4 className="text-sm font-medium text-white">Conversion Settings</h4>
          
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-slate-300">Enable Currency Conversion</Label>
              <p className="text-xs text-slate-400">
                Allow converting between different currencies in your wallet
              </p>
            </div>
            <Switch
              checked={preferences.enableCurrencyConversion || false}
              onCheckedChange={(checked) => handlePreferenceChange('enableCurrencyConversion', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-slate-300">Require Conversion Confirmation</Label>
              <p className="text-xs text-slate-400">
                Show confirmation dialog before executing currency conversions
              </p>
            </div>
            <Switch
              checked={preferences.conversionConfirmationRequired || false}
              onCheckedChange={(checked) => handlePreferenceChange('conversionConfirmationRequired', checked)}
              disabled={!preferences.enableCurrencyConversion}
            />
          </div>
        </div>

        {/* Preview */}
        {preferences.preferredCurrency && (
          <div className="p-4 bg-blue-900/20 border border-blue-700/50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-100 mb-2">Preview</h4>
            <div className="space-y-1 text-sm">
              <div className="text-blue-200">
                Sample amount: {formatCurrency(100, preferences.preferredCurrency)}
              </div>
              <div className="text-blue-300">
                Your wallets will default to {preferences.preferredCurrency}
              </div>
              <div className="text-blue-300">
                Prices will be displayed in {preferences.displayCurrency}
              </div>
            </div>
          </div>
        )}

        {/* Save Button */}
        <Button
          onClick={handleSavePreferences}
          disabled={isSaving}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Saving Preferences...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Currency Preferences
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
