"use client"

import {
  ProductTemplate,
  DynamicField,
  ProductOrder,
  OrderPricing,
  PriceModifier,
  OrderEvent,
  OrderStatus,
  ProcessingType,
  CheckoutUserDetails,
  Currency,
  PricingCalculationRequest,
  PricingCalculationResponse,
  ExchangeRate
} from "@/lib/types"
import { generateOrderId } from "./generateOrderId"
import { attemptAutoProcessing, canProcessAutomatically, getProcessingRecommendation } from "./apiIntegration"
import { formatCurrency, getCurrencyDisplayInfo, validateCurrencyCode } from "./currency"

// ===== ENHANCED ORDER PRICING CALCULATIONS =====

/**
 * Calculate comprehensive order pricing from template and form data
 * Enhanced with multi-currency support and exchange rates
 */
export function calculateOrderPricing(
  template: ProductTemplate,
  formData: Record<string, any>,
  currency: Currency = "SDG",
  options?: {
    basePriceUSD?: number
    exchangeRate?: number
    includeConversionFees?: boolean
  }
): OrderPricing {
  let basePrice = 0
  let basePriceUSD = options?.basePriceUSD
  const modifiers: PriceModifier[] = []
  let quantity = 1
  const exchangeRate = options?.exchangeRate
  const includeConversionFees = options?.includeConversionFees || false

  // Process each field for pricing impact
  template.fields.forEach(field => {
    const fieldValue = formData[field.name]
    if (!fieldValue) return

    switch (field.type) {
      case "package_selector":
        // Package selector sets base price
        if (fieldValue && typeof fieldValue === "object" && fieldValue.price) {
          basePrice = fieldValue.price
          modifiers.push({
            fieldName: field.name,
            fieldLabel: field.label,
            modifier: fieldValue.price,
            type: "override",
            source: "package"
          })
        }
        break

      case "quantity_selector":
        // Quantity multiplies final price
        if (typeof fieldValue === "number" && fieldValue > 0) {
          quantity = fieldValue
        }
        break

      case "select":
      case "radio":
        // Options can add price modifiers
        const selectField = field as any
        if (selectField.options) {
          const selectedOption = selectField.options.find((opt: any) => opt.value === fieldValue)
          if (selectedOption && selectedOption.price && selectedOption.price > 0) {
            modifiers.push({
              fieldName: field.name,
              fieldLabel: field.label,
              modifier: selectedOption.price,
              type: "add",
              source: "option"
            })
          }
        }
        break

      case "grouped_packages":
        // Grouped packages can override base price
        if (fieldValue && typeof fieldValue === "object" && fieldValue.package?.price) {
          basePrice = fieldValue.package.price
          modifiers.push({
            fieldName: field.name,
            fieldLabel: field.label,
            modifier: fieldValue.package.price,
            type: "override",
            source: "package"
          })
        }
        break
    }
  })

  // Calculate subtotal (base price + additive modifiers)
  const additiveModifiers = modifiers.filter(m => m.type === "add")
  const subtotal = basePrice + additiveModifiers.reduce((sum, mod) => sum + mod.modifier, 0)

  // Apply quantity
  let totalPrice = subtotal * quantity

  // Apply conversion fees if specified
  let conversionFees = 0
  if (includeConversionFees && exchangeRate && currency !== 'USD') {
    conversionFees = totalPrice * 0.01 // 1% conversion fee - configurable
    totalPrice += conversionFees
  }

  return {
    basePrice,
    basePriceUSD,
    modifiers,
    quantity,
    subtotal,
    totalPrice,
    currency,
    exchangeRate,
    exchangeRateTimestamp: exchangeRate ? new Date() : undefined,
    conversionFees: conversionFees > 0 ? conversionFees : undefined
  }
}

/**
 * Calculate pricing with USD base price and exchange rate
 * New function for multi-currency pricing
 */
export async function calculateOrderPricingWithExchangeRate(
  template: ProductTemplate,
  formData: Record<string, any>,
  request: PricingCalculationRequest
): Promise<PricingCalculationResponse> {
  // Validate currency
  const currencyValidation = validateCurrencyCode(request.targetCurrency)
  if (!currencyValidation.isValid) {
    throw new Error(`Invalid currency: ${currencyValidation.errors.join(', ')}`)
  }

  // Get exchange rate (in production, this would come from database/API)
  const exchangeRate = await getExchangeRateForCurrency(request.targetCurrency)

  // Calculate base price in target currency
  const targetPrice = request.basePriceUSD * exchangeRate

  // Calculate pricing using enhanced function
  const pricing = calculateOrderPricing(template, formData, request.targetCurrency, {
    basePriceUSD: request.basePriceUSD,
    exchangeRate,
    includeConversionFees: request.includeConversionFees
  })

  return {
    basePriceUSD: request.basePriceUSD,
    targetCurrency: request.targetCurrency,
    exchangeRate,
    targetPrice,
    modifiers: pricing.modifiers,
    quantity: pricing.quantity,
    subtotal: pricing.subtotal,
    totalPrice: pricing.totalPrice,
    conversionFees: pricing.conversionFees || 0,
    rateTimestamp: new Date()
  }
}

/**
 * Get exchange rate for currency (placeholder - will be replaced with database lookup)
 */
async function getExchangeRateForCurrency(currency: Currency): Promise<number> {
  // TODO: Replace with actual database lookup or API call
  const mockRates: Record<string, number> = {
    'USD': 1.0,
    'SDG': 450.0,
    'EGP': 30.8,
    'SAR': 3.75,
    'AED': 3.67,
    'EUR': 0.85,
    'GBP': 0.73
  }

  const rate = mockRates[currency]
  if (!rate) {
    throw new Error(`Exchange rate not found for currency: ${currency}`)
  }

  return rate
}

/**
 * Calculate cross-currency conversion
 */
export async function calculateCrossCurrencyPricing(
  basePriceInCurrency: number,
  fromCurrency: Currency,
  toCurrency: Currency
): Promise<{ convertedPrice: number; exchangeRate: number; timestamp: Date }> {
  if (fromCurrency === toCurrency) {
    return {
      convertedPrice: basePriceInCurrency,
      exchangeRate: 1.0,
      timestamp: new Date()
    }
  }

  // Get rates via USD
  const fromRate = await getExchangeRateForCurrency(fromCurrency)
  const toRate = await getExchangeRateForCurrency(toCurrency)

  // Convert to USD first, then to target currency
  const priceInUSD = basePriceInCurrency / fromRate
  const convertedPrice = priceInUSD * toRate
  const crossRate = toRate / fromRate

  return {
    convertedPrice,
    exchangeRate: crossRate,
    timestamp: new Date()
  }
}

/**
 * Determine processing type based on template configuration and form data
 */
export function determineProcessingType(
  template: ProductTemplate,
  formData: Record<string, any>
): ProcessingType {
  // Check if any field requires manual processing
  const hasManualFields = template.fields.some(field => {
    switch (field.type) {

      case "account_type_selector":
        // Check if selected account type requires manual processing
        const accountType = formData[field.name]
        return accountType === "premium" || accountType === "vip"
      case "image":
        // File uploads typically require manual verification
        return !!formData[field.name]
      default:
        return false
    }
  })

  return hasManualFields ? "manual" : "instant"
}

// ===== ORDER CREATION =====

/**
 * Create a new product order from template and form data
 * ## Supabase Integration: This will insert into 'product_orders' table
 */
export function createProductOrder(
  template: ProductTemplate,
  formData: Record<string, any>,
  userDetails: CheckoutUserDetails,
  currency: Currency = "SDG"
): ProductOrder {
  const orderId = generateOrderId("PRD") // PRD prefix for product orders
  const pricing = calculateOrderPricing(template, formData, currency)
  const processingType = determineProcessingType(template, formData)
  const now = new Date()

  // Create initial timeline event
  const initialEvent: OrderEvent = {
    id: `event_${Date.now()}`,
    type: "created",
    description: "تم إنشاء الطلب بنجاح",
    details: {
      templateName: template.name,
      totalPrice: pricing.totalPrice,
      currency
    },
    createdAt: now,
    createdBy: "user"
  }

  const order: ProductOrder = {
    id: orderId,
    type: "product_order",
    templateId: template.id,
    templateName: template.name,
    templateCategory: template.category,
    productData: formData,
    pricing,
    userDetails,
    status: "pending",
    processingType,
    timeline: [initialEvent],
    createdAt: now,
    updatedAt: now,
    // Supabase fields will be populated during database insert
    userId: undefined, // Will be set from auth.user()
    priority: "normal"
  }

  return order
}

// ## Enhanced order creation with API integration support
export async function createProductOrderWithProcessing(
  template: ProductTemplate,
  formData: Record<string, any>,
  userDetails: CheckoutUserDetails,
  currency: Currency = "SDG",
  attemptAutoProcess: boolean = true
): Promise<{
  order: ProductOrder
  autoProcessed: boolean
  processingMessage: string
  recommendation?: {
    recommendation: "auto" | "manual" | "review"
    reasons: string[]
    confidence: number
  }
}> {
  // Create the basic order
  const order = createProductOrder(template, formData, userDetails, currency)

  // Get processing recommendation
  const recommendation = getProcessingRecommendation(order)

  // Attempt auto-processing if enabled and recommended
  if (attemptAutoProcess && recommendation.recommendation === "auto") {
    try {
      const autoProcessResult = await attemptAutoProcessing(order)

      if (autoProcessResult.processed) {
        // Update order with auto-processing results
        order.status = autoProcessResult.newStatus
        order.updatedAt = new Date()

        // Add auto-processing event to timeline
        const autoProcessEvent: OrderEvent = {
          id: `event_auto_${Date.now()}`,
          type: "status_change",
          description: `تم تحديث الطلب تلقائياً: ${autoProcessResult.message}`,
          details: {
            previousStatus: "pending",
            newStatus: autoProcessResult.newStatus,
            processedBy: "system",
            apiResponse: autoProcessResult.apiResponse
          },
          createdAt: new Date(),
          createdBy: "system"
        }

        order.timeline.push(autoProcessEvent)

        return {
          order,
          autoProcessed: true,
          processingMessage: autoProcessResult.message,
          recommendation
        }
      }
    } catch (error) {
      console.error("Auto-processing failed:", error)

      // Add failed auto-processing event
      const failedEvent: OrderEvent = {
        id: `event_auto_failed_${Date.now()}`,
        type: "admin_note",
        description: "فشل في المعالجة التلقائية - تم تحويل الطلب للمعالجة اليدوية",
        details: {
          error: error instanceof Error ? error.message : "خطأ غير معروف",
          fallbackToManual: true
        },
        createdAt: new Date(),
        createdBy: "system"
      }

      order.timeline.push(failedEvent)
    }
  }

  return {
    order,
    autoProcessed: false,
    processingMessage: recommendation.recommendation === "manual"
      ? "الطلب يتطلب معالجة يدوية"
      : "الطلب في انتظار المراجعة",
    recommendation
  }
}

// ## Update order status with API integration support
export async function updateOrderStatusWithAPI(
  orderId: string,
  newStatus: OrderStatus,
  notes?: string,
  adminId?: string,
  attemptAPISync: boolean = false
): Promise<{
  success: boolean
  message: string
  apiSynced?: boolean
}> {
  try {
    // Update local status first
    updateOrderStatus(orderId, newStatus, notes, adminId)

    // Attempt API synchronization if enabled
    if (attemptAPISync && (newStatus === "completed" || newStatus === "failed")) {
      // ## Supabase Integration: This would sync with external APIs
      // For now, we'll simulate the sync
      console.log(`Syncing order ${orderId} status ${newStatus} with external APIs`)

      // Simulate API sync delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      return {
        success: true,
        message: "تم تحديث الحالة ومزامنتها مع الأنظمة الخارجية",
        apiSynced: true
      }
    }

    return {
      success: true,
      message: "تم تحديث حالة الطلب بنجاح"
    }

  } catch (error) {
    console.error("Error updating order status:", error)
    return {
      success: false,
      message: `خطأ في تحديث الحالة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    }
  }
}

// ===== ORDER STATUS MANAGEMENT =====

/**
 * Update order status and add timeline event
 * ## Supabase Integration: This will update 'product_orders' table and insert timeline event
 */
export function updateOrderStatus(
  order: ProductOrder,
  newStatus: OrderStatus,
  adminNotes?: string,
  adminId?: string
): ProductOrder {
  const now = new Date()
  
  // Create status change event
  const statusEvent: OrderEvent = {
    id: `event_${Date.now()}`,
    type: "status_change",
    description: getStatusChangeDescription(order.status, newStatus),
    details: {
      previousStatus: order.status,
      newStatus,
      adminNotes
    },
    createdAt: now,
    createdBy: adminId || "admin"
  }

  const updatedOrder: ProductOrder = {
    ...order,
    status: newStatus,
    updatedAt: now,
    completedAt: newStatus === "completed" ? now : order.completedAt,
    adminNotes: adminNotes || order.adminNotes,
    assignedAdminId: adminId || order.assignedAdminId,
    timeline: [...order.timeline, statusEvent]
  }

  return updatedOrder
}

/**
 * Get Arabic description for status changes
 */
function getStatusChangeDescription(oldStatus: OrderStatus, newStatus: OrderStatus): string {
  const statusDescriptions: Record<OrderStatus, string> = {
    pending: "في الانتظار",
    processing: "قيد المعالجة",
    completed: "مكتمل",
    failed: "فشل",
    cancelled: "ملغي"
  }

  return `تم تغيير حالة الطلب من "${statusDescriptions[oldStatus]}" إلى "${statusDescriptions[newStatus]}"`
}

// ===== ORDER VALIDATION =====

/**
 * Validate order data before creation
 */
export function validateOrderData(
  template: ProductTemplate,
  formData: Record<string, any>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check required fields
  template.fields.forEach(field => {
    if (field.required && (!formData[field.name] || formData[field.name] === "")) {
      errors.push(`الحقل "${field.label}" مطلوب`)
    }
  })

  // Validate pricing
  try {
    const pricing = calculateOrderPricing(template, formData)
    if (pricing.totalPrice <= 0) {
      errors.push("إجمالي السعر يجب أن يكون أكبر من صفر")
    }
  } catch (error) {
    errors.push("خطأ في حساب السعر")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// ===== ORDER FORMATTING =====

/**
 * Format order data for display in admin interface
 */
export function formatOrderForAdmin(order: ProductOrder) {
  return {
    id: order.id,
    customerName: order.userDetails.fullName,
    customerEmail: order.userDetails.email,
    customerPhone: order.userDetails.phone,
    productName: order.templateName,
    category: order.templateCategory,
    totalAmount: order.pricing.totalPrice,
    currency: order.pricing.currency,
    status: order.status,
    processingType: order.processingType,
    priority: order.priority || "normal",
    createdAt: order.createdAt,
    updatedAt: order.updatedAt,
    timelineCount: order.timeline.length,
    hasAttachments: (order.attachments?.length || 0) > 0
  }
}

/**
 * Format field data for display (handles sensitive data masking)
 */
export function formatFieldDataForDisplay(
  field: DynamicField,
  value: any,
  maskSensitive: boolean = true
): string {
  if (!value) return "-"

  switch (field.type) {
    case "password":
      return maskSensitive ? "••••••••" : value
    case "credentials_group":
      if (typeof value === "object") {
        return Object.entries(value)
          .map(([key, val]) => `${key}: ${maskSensitive && key.includes("password") ? "••••••••" : val}`)
          .join(", ")
      }
      return String(value)
    case "package_selector":
    case "grouped_packages":
      if (typeof value === "object" && value.name) {
        return `${value.name} (${value.price} ${field.name.includes("currency") ? value.currency || "SDG" : "ج.س."})`
      }
      return String(value)
    case "image":
      if (typeof value === "object" && value.name) {
        return `📎 ${value.name}`
      }
      return "📎 ملف مرفق"
    case "checkbox":
      return value ? "✓ نعم" : "✗ لا"
    case "select":
    case "radio":
      return String(value)
    default:
      return String(value)
  }
}
