/**
 * ## Real-Time Chat Hook
 * Handles all chat functionality with Supabase real-time integration
 * Provides secure, optimized chat operations
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { ChatMessage, ChatRoom, UserPresence } from '@/lib/types'

interface UseChatProps {
  userId: string
  userType: 'customer' | 'admin'
  selectedChatUserId?: string // For admin: which customer chat to load
}

interface UseChatReturn {
  // Messages
  messages: ChatMessage[]
  isLoadingMessages: boolean
  
  // Chat rooms (for admin)
  chatRooms: ChatRoom[]
  isLoadingRooms: boolean
  
  // Actions
  sendMessage: (message: string, orderId?: string) => Promise<void>
  markAsRead: (messageIds: string[]) => Promise<void>
  
  // Real-time status
  isOnline: boolean
  typingUsers: string[]
  
  // Utilities
  unreadCount: number
  isConnected: boolean
  error: string | null
}

export function useChat({ userId, userType, selectedChatUserId }: UseChatProps): UseChatReturn {
  // State management
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([])
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)
  const [isLoadingRooms, setIsLoadingRooms] = useState(false)
  const [isOnline, setIsOnline] = useState(false)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Refs for cleanup
  const subscriptionRef = useRef<any>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  /**
   * ## Supabase Integration: Load Chat Messages
   * Fetches messages for current conversation with pagination
   */
  const loadMessages = useCallback(async (targetUserId?: string) => {
    setIsLoadingMessages(true)
    setError(null)
    
    try {
      // ## TODO: Replace with Supabase query
      // const { data, error } = await supabase
      //   .from('chats')
      //   .select('*')
      //   .or(
      //     userType === 'customer' 
      //       ? `user_id.eq.${userId}`
      //       : `user_id.eq.${targetUserId || selectedChatUserId}`
      //   )
      //   .order('created_at', { ascending: true })
      //   .limit(50)
      
      // Mock data for development
      const mockMessages: ChatMessage[] = [
        {
          id: '1',
          userId: userType === 'customer' ? userId : selectedChatUserId || '',
          adminId: userType === 'admin' ? userId : undefined,
          message: 'مرحباً! كيف يمكنني مساعدتك؟',
          senderType: 'admin',
          messageType: 'text',
          isRead: true,
          createdAt: new Date(Date.now() - 3600000)
        },
        {
          id: '2',
          userId: userType === 'customer' ? userId : selectedChatUserId || '',
          message: 'أريد شحن حساب كلاش أوف كلانز',
          senderType: 'customer',
          messageType: 'text',
          isRead: true,
          createdAt: new Date(Date.now() - 3000000)
        }
      ]
      
      setMessages(mockMessages)
    } catch (err) {
      setError('فشل في تحميل الرسائل')
      console.error('Error loading messages:', err)
    } finally {
      setIsLoadingMessages(false)
    }
  }, [userId, userType, selectedChatUserId])

  /**
   * ## Supabase Integration: Load Chat Rooms (Admin Only)
   * Fetches all customer conversations with unread counts
   */
  const loadChatRooms = useCallback(async () => {
    if (userType !== 'admin') return
    
    setIsLoadingRooms(true)
    setError(null)
    
    try {
      // ## TODO: Replace with Supabase query with JOINs
      // const { data, error } = await supabase
      //   .from('chats')
      //   .select(`
      //     user_id,
      //     users!inner(id, email, full_name),
      //     created_at,
      //     message,
      //     is_read
      //   `)
      //   .order('created_at', { ascending: false })
      
      // Mock data for development
      const mockRooms: ChatRoom[] = [
        {
          userId: 'user1',
          customerName: 'أحمد محمد',
          customerEmail: '<EMAIL>',
          unreadCount: 3,
          isOnline: true,
          lastSeen: new Date(),
          activeOrders: [],
          createdAt: new Date(Date.now() - 86400000)
        },
        {
          userId: 'user2',
          customerName: 'سارة علي',
          customerEmail: '<EMAIL>',
          unreadCount: 1,
          isOnline: false,
          lastSeen: new Date(Date.now() - 3600000),
          activeOrders: [],
          createdAt: new Date(Date.now() - 172800000)
        }
      ]
      
      setChatRooms(mockRooms)
      
      // Calculate total unread count
      const totalUnread = mockRooms.reduce((sum, room) => sum + room.unreadCount, 0)
      setUnreadCount(totalUnread)
    } catch (err) {
      setError('فشل في تحميل المحادثات')
      console.error('Error loading chat rooms:', err)
    } finally {
      setIsLoadingRooms(false)
    }
  }, [userType])

  /**
   * ## Supabase Integration: Send Message
   * Sends message with real-time broadcast
   */
  const sendMessage = useCallback(async (message: string, orderId?: string) => {
    if (!message.trim()) return
    
    try {
      const newMessage: Omit<ChatMessage, 'id' | 'createdAt'> = {
        userId: userType === 'customer' ? userId : selectedChatUserId || '',
        adminId: userType === 'admin' ? userId : undefined,
        message: message.trim(),
        senderType: userType,
        orderId,
        messageType: 'text',
        isRead: false
      }
      
      // ## TODO: Replace with Supabase insert
      // const { data, error } = await supabase
      //   .from('chats')
      //   .insert([newMessage])
      //   .select()
      //   .single()
      
      // Mock implementation
      const mockMessage: ChatMessage = {
        ...newMessage,
        id: Date.now().toString(),
        createdAt: new Date()
      }
      
      setMessages(prev => [...prev, mockMessage])
      
      // ## TODO: Broadcast typing stopped
      // await supabase.channel('typing').send({
      //   type: 'broadcast',
      //   event: 'typing_stopped',
      //   payload: { userId, userType }
      // })
      
    } catch (err) {
      setError('فشل في إرسال الرسالة')
      console.error('Error sending message:', err)
    }
  }, [userId, userType, selectedChatUserId])

  /**
   * ## Supabase Integration: Mark Messages as Read
   * Updates read status with optimistic updates
   */
  const markAsRead = useCallback(async (messageIds: string[]) => {
    try {
      // Optimistic update
      setMessages(prev => 
        prev.map(msg => 
          messageIds.includes(msg.id) ? { ...msg, isRead: true } : msg
        )
      )
      
      // ## TODO: Replace with Supabase update
      // await supabase
      //   .from('chats')
      //   .update({ is_read: true })
      //   .in('id', messageIds)
      
    } catch (err) {
      console.error('Error marking messages as read:', err)
      // Revert optimistic update on error
      setMessages(prev => 
        prev.map(msg => 
          messageIds.includes(msg.id) ? { ...msg, isRead: false } : msg
        )
      )
    }
  }, [])

  /**
   * ## Supabase Integration: Setup Real-time Subscriptions
   * Handles message updates, typing indicators, and presence
   */
  useEffect(() => {
    // ## TODO: Setup Supabase real-time subscriptions
    // subscriptionRef.current = supabase
    //   .channel('chat')
    //   .on('postgres_changes', {
    //     event: 'INSERT',
    //     schema: 'public',
    //     table: 'chats',
    //     filter: userType === 'customer'
    //       ? `user_id=eq.${userId}`
    //       : undefined
    //   }, handleNewMessage)
    //   .on('broadcast', { event: 'typing' }, handleTyping)
    //   .on('presence', { event: 'sync' }, handlePresence)
    //   .subscribe()

    setIsConnected(true)
    setIsOnline(true) // Set online status for demo

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
      }
    }
  }, [userId, userType, selectedChatUserId])

  // Load initial data
  useEffect(() => {
    loadMessages()
    if (userType === 'admin') {
      loadChatRooms()
    }
  }, [loadMessages, loadChatRooms, userType])

  return {
    messages,
    isLoadingMessages,
    chatRooms,
    isLoadingRooms,
    sendMessage,
    markAsRead,
    isOnline,
    typingUsers,
    unreadCount,
    isConnected,
    error
  }
}
