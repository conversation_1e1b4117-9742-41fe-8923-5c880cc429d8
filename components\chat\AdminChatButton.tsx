/**
 * ## Secure Role-Based Chat Button
 *
 * Floating action button that provides secure access to chat functionality:
 *
 * **Security Features:**
 * - Role validation before rendering
 * - Secure chat modal integration
 * - User authentication checks
 *
 * **Admin Features:**
 * - Multi-customer chat management
 * - Customer profile access
 * - Order management integration
 *
 * **Customer Features:**
 * - Direct support chat access
 * - Simplified interface
 * - Single conversation focus
 */

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageSquare, X, Minimize2, Maximize2 } from 'lucide-react'
import { ChatModal } from './AdminChatModal'
import { useChat } from '@/lib/hooks/useChat'

interface ChatButtonProps {
  userId: string
  userName?: string
  userEmail?: string
  userRole: 'admin' | 'customer'
  // Position of the floating button
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  // Custom styling
  className?: string
}

export function ChatButton({
  userId,
  userName,
  userEmail,
  userRole,
  position = 'bottom-right',
  className = ''
}: ChatButtonProps) {

  // ## Security: Role and User Validation
  if (!userRole || !['admin', 'customer'].includes(userRole)) {
    console.error('ChatButton: Invalid or missing userRole')
    return null
  }

  if (!userId) {
    console.error('ChatButton: Missing required userId')
    return null
  }

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [hasNewMessages, setHasNewMessages] = useState(false)

  // Get unread count from chat hook
  const { unreadCount } = useChat({
    userId,
    userType: userRole
  })

  // Animate button when new messages arrive
  useEffect(() => {
    if (unreadCount > 0 && !isModalOpen) {
      setHasNewMessages(true)
      const timer = setTimeout(() => setHasNewMessages(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [unreadCount, isModalOpen])

  // Position classes for floating button
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6'
      case 'top-right':
        return 'top-6 right-6'
      case 'top-left':
        return 'top-6 left-6'
      default:
        return 'bottom-6 right-6'
    }
  }

  return (
    <>
      {/* Floating Chat Button */}
      {!isModalOpen && (
        <div className={`fixed ${getPositionClasses()} z-40 ${className}`}>
          <Button
            onClick={() => setIsModalOpen(true)}
            className={`
              w-14 h-14 rounded-full shadow-lg
              bg-gradient-to-r from-green-500 to-green-600
              hover:from-green-600 hover:to-green-700
              text-white border-0
              transition-all duration-300
              ${hasNewMessages ? 'animate-bounce' : 'hover:scale-110'}
              focus:outline-none focus:ring-4 focus:ring-green-500/30
            `}
            aria-label={userRole === 'customer' ? 'فتح الدعم الفني' : 'فتح المحادثات'}
          >
            <MessageSquare className="w-6 h-6" />
            
            {/* Unread Badge */}
            {unreadCount > 0 && (
              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5 min-w-[20px] h-5 flex items-center justify-center animate-pulse">
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}

            {/* Pulse animation for new messages */}
            {hasNewMessages && (
              <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-75" />
            )}
          </Button>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
              {userRole === 'customer' ? 'الدعم الفني' : 'المحادثات'} {unreadCount > 0 && `(${unreadCount} غير مقروءة)`}
            </div>
          </div>
        </div>
      )}

      {/* Chat Modal */}
      <ChatModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false)
          setIsMinimized(false)
        }}
        userId={userId}
        userName={userName}
        userEmail={userEmail}
        userRole={userRole}
        position={position === 'bottom-left' || position === 'top-left' ? 'bottom-left' : 'bottom-right'}
        isMinimized={isMinimized}
        onToggleMinimize={() => setIsMinimized(!isMinimized)}
      />
    </>
  )
}

// Backward compatibility alias
export const AdminChatButton = ChatButton

/**
 * ## Admin Chat Widget - Integrated Version
 * For embedding directly in admin dashboard layout
 */
interface AdminChatWidgetProps {
  userId: string
  userName?: string
  userEmail?: string
  className?: string
}

export function AdminChatWidget({ 
  userId, 
  userName, 
  userEmail,
  className = ''
}: AdminChatWidgetProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const { unreadCount } = useChat({
    userId,
    userType: 'admin'
  })

  return (
    <div className={`bg-slate-800 rounded-lg border border-slate-700 ${className}`}>
      {/* Widget Header */}
      <div 
        className="p-4 border-b border-slate-700 cursor-pointer hover:bg-slate-700/30 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
              <MessageSquare className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white">المحادثات</h3>
              <p className="text-xs text-slate-400">
                {unreadCount > 0 ? `${unreadCount} رسالة غير مقروءة` : 'لا توجد رسائل جديدة'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Badge className="bg-green-500 text-white text-xs">
                {unreadCount}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="p-1"
            >
              {isExpanded ? (
                <Minimize2 className="h-4 w-4 text-slate-400" />
              ) : (
                <Maximize2 className="h-4 w-4 text-slate-400" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Widget Content */}
      {isExpanded && (
        <div className="h-96">
          <AdminChatModal
            isOpen={true}
            onClose={() => setIsExpanded(false)}
            userId={userId}
            userName={userName}
            userEmail={userEmail}
            position="center"
            isMinimized={false}
          />
        </div>
      )}
    </div>
  )
}

/**
 * ## Admin Chat Notification Bar
 * Compact notification bar for admin header
 */
interface AdminChatNotificationProps {
  userId: string
  onOpenChat: () => void
  className?: string
}

export function AdminChatNotification({ 
  userId, 
  onOpenChat,
  className = ''
}: AdminChatNotificationProps) {
  const { unreadCount, chatRooms } = useChat({
    userId,
    userType: 'admin'
  })

  if (unreadCount === 0) return null

  const activeCustomers = chatRooms.filter(room => room.unreadCount > 0)

  return (
    <div className={`bg-green-500/10 border border-green-500/20 rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <MessageSquare className="h-4 w-4 text-white" />
          </div>
          <div>
            <p className="text-green-400 font-medium text-sm">
              {unreadCount} رسالة جديدة من {activeCustomers.length} عميل
            </p>
            <p className="text-green-300 text-xs">
              {activeCustomers.slice(0, 2).map(room => room.customerName).join(', ')}
              {activeCustomers.length > 2 && ` و ${activeCustomers.length - 2} آخرين`}
            </p>
          </div>
        </div>

        <Button
          onClick={onOpenChat}
          className="bg-green-500 hover:bg-green-600 text-white text-sm px-4 py-2"
        >
          عرض المحادثات
        </Button>
      </div>
    </div>
  )
}

/**
 * ## Usage Examples:
 * 
 * // Floating button (recommended for most admin pages)
 * <AdminChatButton userId="admin-123" />
 * 
 * // Integrated widget (for dashboard)
 * <AdminChatWidget userId="admin-123" className="col-span-1" />
 * 
 * // Notification bar (for admin header)
 * <AdminChatNotification 
 *   userId="admin-123" 
 *   onOpenChat={() => setShowChat(true)} 
 * />
 */
