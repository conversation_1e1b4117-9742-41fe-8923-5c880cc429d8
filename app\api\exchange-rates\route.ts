import { NextRequest, NextResponse } from 'next/server'
import {
  <PERSON>ur<PERSON>cy,
  ExchangeRate,
  ExchangeRateUpdateRequest,
  ExchangeRateUpdateResponse
} from '@/lib/types'

// Mock exchange rates data
const mockExchangeRates: ExchangeRate[] = [
  {
    id: '1',
    fromCurrencyCode: 'USD',
    toCurrencyCode: 'SDG',
    rate: 450.00,
    effectiveDate: new Date(),
    createdAt: new Date(),
    isActive: true
  },
  {
    id: '2',
    fromCurrencyCode: 'USD',
    toCurrencyCode: 'EGP',
    rate: 30.80,
    effectiveDate: new Date(),
    createdAt: new Date(),
    isActive: true
  },
  {
    id: '3',
    fromCurrencyCode: 'SDG',
    toCurrencyCode: 'USD',
    rate: 1/450.00,
    effectiveDate: new Date(),
    createdAt: new Date(),
    isActive: true
  },
  {
    id: '4',
    fromCurrencyCode: 'EGP',
    toCurrencyCode: 'USD',
    rate: 1/30.80,
    effectiveDate: new Date(),
    createdAt: new Date(),
    isActive: true
  }
]

/**
 * GET /api/exchange-rates
 * Fetch current exchange rates
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fromCurrency = searchParams.get('from')
    const toCurrency = searchParams.get('to')
    const baseCurrency = searchParams.get('base') || 'USD'

    if (fromCurrency && toCurrency) {
      // Get specific rate
      const rate = await getExchangeRate(fromCurrency, toCurrency)
      return NextResponse.json({
        success: true,
        rate: {
          fromCurrency,
          toCurrency,
          rate,
          timestamp: new Date()
        }
      })
    } else {
      // Get all rates for base currency
      const rates = await getAllExchangeRates(baseCurrency)
      return NextResponse.json({
        success: true,
        baseCurrency,
        rates,
        timestamp: new Date()
      })
    }

  } catch (error) {
    console.error('Error fetching exchange rates:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch exchange rates',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/exchange-rates
 * Update exchange rates
 */
export async function POST(request: NextRequest) {
  try {
    const body: ExchangeRateUpdateRequest = await request.json()

    // Validate request
    if (!body.rates || !Array.isArray(body.rates) || body.rates.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Rates array is required and must not be empty' 
        },
        { status: 400 }
      )
    }

    const response = await updateExchangeRates(body)
    
    return NextResponse.json(response, {
      status: response.success ? 200 : 400
    })

  } catch (error) {
    console.error('Error updating exchange rates:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update exchange rates',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper functions

async function getExchangeRate(fromCurrency: Currency, toCurrency: Currency): Promise<number> {
  // Same currency
  if (fromCurrency === toCurrency) {
    return 1.0
  }

  try {
    // Try direct rate first
    const { data: directRate } = await supabase
      .rpc('get_exchange_rate', {
        p_from_currency: fromCurrency,
        p_to_currency: toCurrency
      })

    if (directRate) {
      return directRate
    }

    // If no direct rate, calculate via USD
    const { data: fromUsdRate } = await supabase
      .from('exchange_rates')
      .select('rate')
      .eq('from_currency_code', 'USD')
      .eq('to_currency_code', fromCurrency)
      .eq('is_active', true)
      .order('effective_date', { ascending: false })
      .limit(1)
      .single()

    const { data: toUsdRate } = await supabase
      .from('exchange_rates')
      .select('rate')
      .eq('from_currency_code', 'USD')
      .eq('to_currency_code', toCurrency)
      .eq('is_active', true)
      .order('effective_date', { ascending: false })
      .limit(1)
      .single()

    if (fromUsdRate && toUsdRate) {
      return toUsdRate.rate / fromUsdRate.rate
    }

    throw new Error(`Exchange rate not found for ${fromCurrency} to ${toCurrency}`)

  } catch (error) {
    console.error(`Error getting exchange rate for ${fromCurrency} to ${toCurrency}:`, error)
    throw error
  }
}

async function getAllExchangeRates(baseCurrency: Currency): Promise<Record<Currency, number>> {
  try {
    const { data: rates, error } = await supabase
      .from('exchange_rates')
      .select('to_currency_code, rate')
      .eq('from_currency_code', baseCurrency)
      .eq('is_active', true)
      .order('effective_date', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch rates: ${error.message}`)
    }

    const ratesMap: Record<Currency, number> = {}
    
    // Add base currency rate
    ratesMap[baseCurrency] = 1.0

    // Add other rates
    rates?.forEach(rate => {
      ratesMap[rate.to_currency_code] = rate.rate
    })

    return ratesMap

  } catch (error) {
    console.error(`Error getting all exchange rates for ${baseCurrency}:`, error)
    throw error
  }
}

async function updateExchangeRates(request: ExchangeRateUpdateRequest): Promise<ExchangeRateUpdateResponse> {
  const updatedRates: ExchangeRate[] = []
  const errors: string[] = []

  try {
    for (const rateUpdate of request.rates) {
      try {
        // Validate rate
        if (rateUpdate.rate <= 0) {
          errors.push(`Invalid rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: Rate must be positive`)
          continue
        }

        if (rateUpdate.fromCurrency === rateUpdate.toCurrency && rateUpdate.rate !== 1) {
          errors.push(`Invalid rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: Same currency rate must be 1.0`)
          continue
        }

        // Deactivate old rates
        await supabase
          .from('exchange_rates')
          .update({ is_active: false })
          .eq('from_currency_code', rateUpdate.fromCurrency)
          .eq('to_currency_code', rateUpdate.toCurrency)

        // Insert new rate
        const { data, error } = await supabase
          .from('exchange_rates')
          .insert({
            from_currency_code: rateUpdate.fromCurrency,
            to_currency_code: rateUpdate.toCurrency,
            rate: rateUpdate.rate,
            effective_date: request.effectiveDate || new Date().toISOString(),
            is_active: true
          })
          .select()
          .single()

        if (error) {
          errors.push(`Failed to update rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: ${error.message}`)
          continue
        }

        updatedRates.push({
          id: data.id,
          fromCurrencyCode: data.from_currency_code,
          toCurrencyCode: data.to_currency_code,
          rate: data.rate,
          effectiveDate: new Date(data.effective_date),
          createdAt: new Date(data.created_at),
          isActive: data.is_active
        })

      } catch (error) {
        errors.push(`Error updating rate for ${rateUpdate.fromCurrency} to ${rateUpdate.toCurrency}: ${error}`)
      }
    }

    return {
      success: errors.length === 0,
      message: errors.length === 0 
        ? `Successfully updated ${updatedRates.length} exchange rates`
        : `Updated ${updatedRates.length} rates with ${errors.length} errors`,
      updatedRates,
      errors: errors.length > 0 ? errors : undefined
    }

  } catch (error) {
    console.error('Error in updateExchangeRates:', error)
    return {
      success: false,
      message: 'Failed to update exchange rates',
      updatedRates: [],
      errors: [`Internal server error: ${error}`]
    }
  }
}

/**
 * PUT /api/exchange-rates
 * Bulk update exchange rates with validation
 */
export async function PUT(request: NextRequest) {
  try {
    const { rates, source } = await request.json()

    if (!rates || typeof rates !== 'object') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Rates object is required' 
        },
        { status: 400 }
      )
    }

    // Convert rates object to array format
    const rateUpdates = Object.entries(rates).map(([currency, rate]) => ({
      fromCurrency: 'USD' as Currency,
      toCurrency: currency as Currency,
      rate: rate as number
    }))

    const response = await updateExchangeRates({
      rates: rateUpdates,
      effectiveDate: new Date(),
      source
    })

    return NextResponse.json(response, {
      status: response.success ? 200 : 400
    })

  } catch (error) {
    console.error('Error in bulk update exchange rates:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to bulk update exchange rates',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
