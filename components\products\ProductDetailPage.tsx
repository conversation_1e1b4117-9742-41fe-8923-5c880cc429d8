"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>outer } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Star,
  Clock,
  Zap,
  ShoppingCart,
  Plus,
  Minus,
  Check,
  Shield,
  Truck,
  HeadphonesIcon,
  Gift,
  Users,
  MessageCircle,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { formatCurrency } from "@/lib/data/currencies"
import { realisticProducts } from "@/lib/data/realisticProducts"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"

// Utility function to format numbers consistently (prevents hydration errors)
const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return `${Math.floor(num / 1000)}k`
  }
  return num.toString()
}

interface Package {
  id: string
  name: string
  amount: string
  price: number
  originalPrice?: number
  discount?: number
  popular?: boolean
}

interface ProductDetailPageProps {
  product: {
    id: string
    title: string
    shortDescription: string
    image: string
    category: string
    rating: number
    reviewsCount: number
    priceFrom: number
    estimatedTime: string
    popular?: boolean
    features: string[]
    packages?: Package[]
    packageGroups?: any[]
    type: "instant" | "manual"
  }
}

export function ProductDetailPage({ product }: ProductDetailPageProps) {
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("shop")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const router = useRouter()

  // Use global currency context for price conversion
  const { formatPrice, selectedCurrency } = useCurrencyConverter()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Handle package selection
  const handlePackageSelect = (pkg: Package) => {
    setSelectedPackage(pkg)
  }

  // Handle quantity change
  const handleQuantityChange = (change: number) => {
    const newQuantity = Math.max(1, quantity + change)
    setQuantity(newQuantity)
  }

  // Handle form input change
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Calculate total price
  const totalPrice = selectedPackage ? selectedPackage.price * quantity : 0
  const originalTotal = selectedPackage?.originalPrice ? selectedPackage.originalPrice * quantity : totalPrice
  const savings = originalTotal - totalPrice

  // Handle add to cart
  const handleAddToCart = () => {
    if (!selectedPackage) return
    setIsLoading(true)
    // Add to cart logic here
    setTimeout(() => {
      setIsLoading(false)
      // Show success message
    }, 1000)
  }

  // Handle buy now
  const handleBuyNow = () => {
    if (!selectedPackage) return
    // Redirect to checkout
    window.location.href = `/checkout?product=${product.id}&package=${selectedPackage.id}&quantity=${quantity}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>

      {/* Standard Navigation */}
      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />

      <SideMenu
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
      />

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-7xl pt-32 pb-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          
          {/* Left Column - Product Image & Info */}
          <div className="space-y-6">
            {/* Product Image */}
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
              <CardContent className="p-0">
                <div className="relative aspect-square">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center">
                      <Gift className="h-24 w-24 text-slate-400" />
                    </div>
                  )}
                  
                  {/* Badges */}
                  <div className="absolute top-4 right-4 space-y-2">
                    {product.popular && (
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 font-bold">
                        🔥 الأكثر طلباً
                      </Badge>
                    )}
                    <Badge 
                      className={`${
                        product.type === 'instant' 
                          ? 'bg-green-500/20 text-green-400 border-green-500/30' 
                          : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                      }`}
                    >
                      {product.type === 'instant' ? (
                        <>
                          <Zap className="h-3 w-3 mr-1" />
                          شحن فوري
                        </>
                      ) : (
                        <>
                          <Clock className="h-3 w-3 mr-1" />
                          شحن يدوي
                        </>
                      )}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Features */}
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-400" />
                  مميزات المنتج
                </h3>
                <div className="space-y-3">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                      <span className="text-slate-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Product Details & Purchase */}
          <div className="space-y-6">
            {/* Product Header */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline" className="text-yellow-400 border-yellow-400/30">
                  {product.category}
                </Badge>
              </div>
              
              <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
                {product.title}
              </h1>
              
              <p className="text-slate-300 text-lg mb-4">
                {product.shortDescription}
              </p>

              {/* Rating & Reviews */}
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-1">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="text-white font-medium">{product.rating}</span>
                  <span className="text-slate-400">({formatNumber(product.reviewsCount)} تقييم)</span>
                </div>
                <div className="flex items-center gap-1 text-slate-400">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">{product.estimatedTime}</span>
                </div>
              </div>

              {/* Price Range */}
              <div className="flex items-center gap-2 mb-6">
                <span className="text-2xl font-bold text-yellow-400">
                  من {formatPrice(product.priceFrom)}
                </span>
                <span className="text-slate-400">حسب الحزمة المختارة</span>
              </div>
            </div>

            <Separator className="bg-slate-700/50" />

            {/* Package Selection */}
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-white mb-4">اختر الحزمة</h3>
                
                {product.packages && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {product.packages.map((pkg) => (
                      <button
                        key={pkg.id}
                        onClick={() => handlePackageSelect(pkg)}
                        className={`relative p-4 rounded-lg border-2 transition-all duration-300 text-center ${
                          selectedPackage?.id === pkg.id
                            ? 'border-yellow-400 bg-yellow-400/10'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        {pkg.popular && (
                          <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs">
                            الأشهر
                          </Badge>
                        )}
                        
                        <div className="text-white font-medium text-sm mb-1">
                          {pkg.name}
                        </div>
                        <div className="text-slate-400 text-xs mb-2">
                          {pkg.amount}
                        </div>
                        
                        <div className="space-y-1">
                          <div className="text-yellow-400 font-bold text-sm">
                            {formatPrice(pkg.price)}
                          </div>
                          {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                            <div className="flex items-center justify-center gap-1">
                              <span className="text-slate-500 line-through text-xs">
                                {formatPrice(pkg.originalPrice)}
                              </span>
                              {pkg.discount && (
                                <Badge className="bg-red-500/20 text-red-400 text-xs">
                                  -{pkg.discount}%
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Dynamic Form Fields */}
            {selectedPackage && (
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">معلومات الحساب</h3>

                  <div className="space-y-4">
                    {/* Player ID Field */}
                    <div>
                      <Label htmlFor="playerId" className="text-slate-300 mb-2 block">
                        معرف اللاعب (Player ID) *
                      </Label>
                      <Input
                        id="playerId"
                        placeholder="أدخل معرف اللاعب الخاص بك"
                        value={formData.playerId || ""}
                        onChange={(e) => handleInputChange("playerId", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                      />
                    </div>

                    {/* Server Selection */}
                    <div>
                      <Label className="text-slate-300 mb-2 block">
                        اختر السيرفر *
                      </Label>
                      <Select onValueChange={(value) => handleInputChange("server", value)}>
                        <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                          <SelectValue placeholder="اختر السيرفر" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="global">Global - عالمي</SelectItem>
                          <SelectItem value="mena">MENA - الشرق الأوسط وشمال أفريقيا</SelectItem>
                          <SelectItem value="asia">Asia - آسيا</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Account Type */}
                    <div>
                      <Label className="text-slate-300 mb-2 block">
                        نوع تسجيل الدخول *
                      </Label>
                      <Select onValueChange={(value) => handleInputChange("accountType", value)}>
                        <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                          <SelectValue placeholder="اختر نوع الحساب" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-700">
                          <SelectItem value="facebook">Facebook</SelectItem>
                          <SelectItem value="google">Google</SelectItem>
                          <SelectItem value="email">البريد الإلكتروني</SelectItem>
                          <SelectItem value="phone">رقم الهاتف</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* WhatsApp Number */}
                    <div>
                      <Label htmlFor="whatsapp" className="text-slate-300 mb-2 block">
                        رقم الواتساب للتواصل *
                      </Label>
                      <Input
                        id="whatsapp"
                        placeholder="+249xxxxxxxxx"
                        value={formData.whatsapp || ""}
                        onChange={(e) => handleInputChange("whatsapp", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quantity & Purchase */}
            {selectedPackage && (
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    {/* Quantity Selector */}
                    <div>
                      <Label className="text-slate-300 mb-3 block">الكمية</Label>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuantityChange(-1)}
                          disabled={quantity <= 1}
                          className="border-slate-600 text-white hover:bg-slate-700"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="text-white font-medium text-lg min-w-[3rem] text-center">
                          {quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleQuantityChange(1)}
                          className="border-slate-600 text-white hover:bg-slate-700"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Price Summary */}
                    <div className="bg-slate-700/30 rounded-lg p-4 space-y-2">
                      <div className="flex justify-between text-slate-300">
                        <span>سعر الوحدة:</span>
                        <span>{formatPrice(selectedPackage.price)}</span>
                      </div>
                      <div className="flex justify-between text-slate-300">
                        <span>الكمية:</span>
                        <span>{quantity}</span>
                      </div>
                      {savings > 0 && (
                        <div className="flex justify-between text-green-400">
                          <span>التوفير:</span>
                          <span>-{formatPrice(savings)}</span>
                        </div>
                      )}
                      <Separator className="bg-slate-600" />
                      <div className="flex justify-between text-white font-bold text-lg">
                        <span>المجموع:</span>
                        <span className="text-yellow-400">{formatPrice(totalPrice)}</span>
                      </div>
                    </div>

                    {/* Purchase Buttons */}
                    <div className="space-y-3">
                      <Button
                        onClick={handleBuyNow}
                        className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 text-lg"
                        size="lg"
                      >
                        <Zap className="h-5 w-5 mr-2" />
                        اشتري الآن
                      </Button>

                      <Button
                        onClick={handleAddToCart}
                        variant="outline"
                        className="w-full border-slate-600 text-white hover:bg-slate-700 py-3"
                        size="lg"
                        disabled={isLoading}
                      >
                        <ShoppingCart className="h-5 w-5 mr-2" />
                        {isLoading ? "جاري الإضافة..." : "أضف للسلة"}
                      </Button>
                    </div>

                    {/* Trust Indicators */}
                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <div className="flex items-center gap-2 text-slate-400 text-sm">
                        <Shield className="h-4 w-4 text-green-400" />
                        <span>دفع آمن</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400 text-sm">
                        <Truck className="h-4 w-4 text-blue-400" />
                        <span>تسليم سريع</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400 text-sm">
                        <HeadphonesIcon className="h-4 w-4 text-purple-400" />
                        <span>دعم 24/7</span>
                      </div>
                      <div className="flex items-center gap-2 text-slate-400 text-sm">
                        <Users className="h-4 w-4 text-yellow-400" />
                        <span>+{product.reviewsCount.toLocaleString()} عميل</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-blue-400" />
                  تعليمات مهمة
                </h3>
                <div className="space-y-2">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300 text-sm">تأكد من إدخال معرف اللاعب بشكل صحيح</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300 text-sm">اختر السيرفر المناسب لمنطقتك</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300 text-sm">تأكد من أن حسابك نشط ومتاح للشحن</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300 text-sm">في حالة وجود مشكلة، تواصل معنا فوراً</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Related Products Section */}
        <RelatedProductsSection currentProductId={product.id} />
      </div>

      {/* Standard Mobile Navigation */}
      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}

// Related Products Component
interface RelatedProductsSectionProps {
  currentProductId: string
}

function RelatedProductsSection({ currentProductId }: RelatedProductsSectionProps) {
  const [scrollPosition, setScrollPosition] = useState(0)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)
  const router = useRouter()

  // Get related products (exclude current product)
  const relatedProducts = realisticProducts
    .filter(product => product.id !== currentProductId)
    .slice(0, 8) // Limit to 8 products

  // Update scroll state
  const updateScrollState = () => {
    const container = document.getElementById('related-products-container')
    if (container) {
      const { scrollLeft, scrollWidth, clientWidth } = container
      setScrollPosition(scrollLeft)
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10)
    }
  }

  // Initialize scroll state on mount
  useEffect(() => {
    const timer = setTimeout(updateScrollState, 100) // Small delay to ensure DOM is ready
    return () => clearTimeout(timer)
  }, [])

  const handleProductClick = (productId: string) => {
    router.push(`/shop/${productId}`)
  }

  const handleScroll = (direction: 'left' | 'right') => {
    const container = document.getElementById('related-products-container')
    if (container) {
      const scrollAmount = 150 // Further reduced for smaller cards
      const newPosition = direction === 'left'
        ? Math.max(0, scrollPosition - scrollAmount)
        : Math.min(container.scrollWidth - container.clientWidth, scrollPosition + scrollAmount)

      container.scrollTo({ left: newPosition, behavior: 'smooth' })
      setTimeout(updateScrollState, 300) // Update state after scroll animation
    }
  }

  if (relatedProducts.length === 0) return null

  return (
    <div className="mt-16 mb-8 animate-in fade-in-50 slide-in-from-bottom-4 duration-700">
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-6 lg:p-8">
          {/* Section Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                منتجات ذات صلة
              </h2>
              <p className="text-slate-400">منتجات أخرى قد تعجبك</p>
            </div>

            {/* Navigation Arrows - Desktop Only */}
            <div className="hidden lg:flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleScroll('left')}
                disabled={!canScrollLeft}
                className={`border-slate-600 transition-all duration-300 ${
                  canScrollLeft
                    ? 'text-slate-400 hover:text-white hover:bg-slate-700'
                    : 'text-slate-600 cursor-not-allowed'
                }`}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleScroll('right')}
                disabled={!canScrollRight}
                className={`border-slate-600 transition-all duration-300 ${
                  canScrollRight
                    ? 'text-slate-400 hover:text-white hover:bg-slate-700'
                    : 'text-slate-600 cursor-not-allowed'
                }`}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Products Carousel */}
          <div className="relative">
            <div
              id="related-products-container"
              className="flex gap-3 lg:gap-4 overflow-x-auto scrollbar-hide pb-4 snap-x snap-mandatory"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              onScroll={updateScrollState}
            >
              {relatedProducts.map((relatedProduct, index) => (
                <div
                  key={relatedProduct.id}
                  className="flex-shrink-0 w-32 sm:w-40 lg:w-48 snap-start animate-in fade-in-50 slide-in-from-bottom-4"
                  style={{ animationDelay: `${index * 100}ms`, animationDuration: '600ms' }}
                >
                  <Card
                    className="bg-slate-700/50 border-slate-600/50 backdrop-blur-sm hover:bg-slate-600/50 hover:border-yellow-400/30 hover:shadow-xl hover:shadow-yellow-400/10 transition-all duration-300 cursor-pointer group overflow-hidden transform hover:scale-[1.02]"
                    onClick={() => handleProductClick(relatedProduct.id)}
                  >
                    <CardContent className="p-0">
                      {/* Product Image */}
                      <div className="relative aspect-square overflow-hidden">
                        {relatedProduct.image ? (
                          <img
                            src={relatedProduct.image}
                            alt={relatedProduct.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-slate-600 via-slate-500 to-slate-700 flex items-center justify-center">
                            <Gift className="h-8 w-8 text-slate-400" />
                          </div>
                        )}

                        {/* Subtle overlay for better text readability */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>

                      {/* Product Title Only */}
                      <div className="p-3">
                        <h3 className="text-white font-medium text-sm leading-tight line-clamp-2 group-hover:text-yellow-400 transition-colors duration-300">
                          {relatedProduct.title}
                        </h3>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Scroll Indicator */}
          <div className="lg:hidden mt-4 text-center">
            <p className="text-slate-400 text-sm">← اسحب لاستكشاف المزيد →</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
