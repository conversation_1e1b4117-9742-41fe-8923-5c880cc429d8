import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { 
  Currency, 
  RevenueConsolidated,
  RevenueCalculationOptions
} from '@/lib/types'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

/**
 * GET /api/reports/revenue
 * Get consolidated revenue report
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const options: RevenueCalculationOptions = {
      startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
      endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined,
      primaryCurrency: (searchParams.get('primaryCurrency') as Currency) || 'USD',
      includePending: searchParams.get('includePending') === 'true',
      groupByCurrency: searchParams.get('groupByCurrency') === 'true',
      includeExchangeRates: searchParams.get('includeExchangeRates') === 'true'
    }

    const report = await generateRevenueReport(options)
    
    return NextResponse.json({
      success: true,
      report
    })

  } catch (error) {
    console.error('Error generating revenue report:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate revenue report',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/revenue/consolidated
 * Get consolidated revenue with custom parameters
 */
export async function POST(request: NextRequest) {
  try {
    const options: RevenueCalculationOptions = await request.json()

    // Validate options
    if (options.startDate && options.endDate && options.startDate > options.endDate) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Start date must be before end date'
        },
        { status: 400 }
      )
    }

    const report = await generateRevenueReport(options)
    
    return NextResponse.json({
      success: true,
      report
    })

  } catch (error) {
    console.error('Error generating consolidated revenue report:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to generate consolidated revenue report',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper functions

async function generateRevenueReport(options: RevenueCalculationOptions): Promise<RevenueConsolidated> {
  try {
    // Use Supabase RPC function for complex revenue calculation
    const { data, error } = await supabase
      .rpc('calculate_revenue_consolidated', {
        p_start_date: options.startDate?.toISOString(),
        p_end_date: options.endDate?.toISOString(),
        p_primary_currency: options.primaryCurrency || 'USD'
      })

    if (error) {
      console.error('Error in revenue calculation RPC:', error)
      throw new Error(`Revenue calculation failed: ${error.message}`)
    }

    if (!data) {
      throw new Error('No revenue data returned from calculation')
    }

    // Transform the response to match our interface
    const report: RevenueConsolidated = {
      primaryCurrency: data.primary_currency,
      totalRevenue: data.total_revenue_primary,
      revenueByCurrency: data.revenue_by_currency || {},
      exchangeRatesUsed: await getExchangeRatesUsed(data.revenue_by_currency, data.primary_currency),
      calculatedAt: new Date(data.calculated_at),
      periodStart: data.period_start ? new Date(data.period_start) : undefined,
      periodEnd: data.period_end ? new Date(data.period_end) : undefined
    }

    return report

  } catch (error) {
    console.error('Error in generateRevenueReport:', error)
    throw error
  }
}

async function getExchangeRatesUsed(
  revenueByCurrency: Record<Currency, number>,
  primaryCurrency: Currency
): Promise<Record<string, number>> {
  const exchangeRatesUsed: Record<string, number> = {}

  for (const currency of Object.keys(revenueByCurrency)) {
    if (currency !== primaryCurrency) {
      try {
        const { data: rate } = await supabase
          .rpc('get_exchange_rate', {
            p_from_currency: currency,
            p_to_currency: primaryCurrency
          })

        if (rate) {
          exchangeRatesUsed[`${currency}_to_${primaryCurrency}`] = rate
        }
      } catch (error) {
        console.warn(`Failed to get exchange rate for ${currency} to ${primaryCurrency}:`, error)
      }
    }
  }

  return exchangeRatesUsed
}

/**
 * GET /api/reports/revenue/breakdown
 * Get detailed revenue breakdown by currency and time period
 */
export async function GET_BREAKDOWN(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const groupBy = searchParams.get('groupBy') || 'month' // day, week, month, year
    const currency = searchParams.get('currency') as Currency
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined

    let query = supabase
      .from('product_orders')
      .select(`
        total_price,
        currency_code,
        completed_at,
        created_at
      `)
      .eq('status', 'completed')

    if (currency) {
      query = query.eq('currency_code', currency)
    }

    if (startDate) {
      query = query.gte('completed_at', startDate.toISOString())
    }

    if (endDate) {
      query = query.lte('completed_at', endDate.toISOString())
    }

    const { data: orders, error } = await query.order('completed_at', { ascending: true })

    if (error) {
      console.error('Error fetching revenue breakdown:', error)
      throw new Error(`Failed to fetch revenue data: ${error.message}`)
    }

    // Group orders by time period
    const breakdown = groupOrdersByPeriod(orders || [], groupBy)

    return NextResponse.json({
      success: true,
      breakdown,
      groupBy,
      currency,
      periodStart: startDate,
      periodEnd: endDate
    })

  } catch (error) {
    console.error('Error getting revenue breakdown:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get revenue breakdown',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

function groupOrdersByPeriod(orders: any[], groupBy: string) {
  const groups: Record<string, { revenue: number; orders: number; currencies: Record<Currency, number> }> = {}

  orders.forEach(order => {
    const date = new Date(order.completed_at || order.created_at)
    let periodKey: string

    switch (groupBy) {
      case 'day':
        periodKey = date.toISOString().split('T')[0]
        break
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        periodKey = weekStart.toISOString().split('T')[0]
        break
      case 'month':
        periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      case 'year':
        periodKey = String(date.getFullYear())
        break
      default:
        periodKey = date.toISOString().split('T')[0]
    }

    if (!groups[periodKey]) {
      groups[periodKey] = {
        revenue: 0,
        orders: 0,
        currencies: {}
      }
    }

    groups[periodKey].revenue += order.total_price
    groups[periodKey].orders += 1
    
    const currency = order.currency_code
    groups[periodKey].currencies[currency] = (groups[periodKey].currencies[currency] || 0) + order.total_price
  })

  // Convert to array and sort by period
  return Object.entries(groups)
    .map(([period, data]) => ({
      period,
      ...data
    }))
    .sort((a, b) => a.period.localeCompare(b.period))
}

/**
 * GET /api/reports/revenue/summary
 * Get revenue summary statistics
 */
export async function GET_SUMMARY(request: NextRequest) {
  try {
    const { data: summary, error } = await supabase
      .from('product_orders')
      .select(`
        total_price,
        currency_code,
        status,
        created_at,
        completed_at
      `)

    if (error) {
      console.error('Error fetching revenue summary:', error)
      throw new Error(`Failed to fetch revenue summary: ${error.message}`)
    }

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const thisYear = new Date(now.getFullYear(), 0, 1)

    const stats = {
      total: {
        orders: summary?.length || 0,
        revenue: 0,
        revenueByCurrency: {} as Record<Currency, number>
      },
      today: {
        orders: 0,
        revenue: 0,
        revenueByCurrency: {} as Record<Currency, number>
      },
      thisMonth: {
        orders: 0,
        revenue: 0,
        revenueByCurrency: {} as Record<Currency, number>
      },
      thisYear: {
        orders: 0,
        revenue: 0,
        revenueByCurrency: {} as Record<Currency, number>
      }
    }

    summary?.forEach(order => {
      const orderDate = new Date(order.completed_at || order.created_at)
      const revenue = order.total_price
      const currency = order.currency_code

      if (order.status === 'completed') {
        // Total stats
        stats.total.revenue += revenue
        stats.total.revenueByCurrency[currency] = (stats.total.revenueByCurrency[currency] || 0) + revenue

        // Today stats
        if (orderDate >= today) {
          stats.today.orders += 1
          stats.today.revenue += revenue
          stats.today.revenueByCurrency[currency] = (stats.today.revenueByCurrency[currency] || 0) + revenue
        }

        // This month stats
        if (orderDate >= thisMonth) {
          stats.thisMonth.orders += 1
          stats.thisMonth.revenue += revenue
          stats.thisMonth.revenueByCurrency[currency] = (stats.thisMonth.revenueByCurrency[currency] || 0) + revenue
        }

        // This year stats
        if (orderDate >= thisYear) {
          stats.thisYear.orders += 1
          stats.thisYear.revenue += revenue
          stats.thisYear.revenueByCurrency[currency] = (stats.thisYear.revenueByCurrency[currency] || 0) + revenue
        }
      }
    })

    return NextResponse.json({
      success: true,
      summary: stats,
      generatedAt: new Date()
    })

  } catch (error) {
    console.error('Error getting revenue summary:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get revenue summary',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
