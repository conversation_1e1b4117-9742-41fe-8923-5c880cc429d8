"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { BankOptionCard } from "@/components/checkout/BankOptionCard"
import { UploadReceipt } from "@/components/checkout/UploadReceipt"
import { useCheckout } from "@/components/checkout/CheckoutContext"
import { getActiveBankAccounts } from "@/lib/utils/localStorage"
import { formatCurrency } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"
import { ArrowRight, CreditCard, Hash, Upload } from "lucide-react"
import { BankAccount } from "@/lib/types"

interface Step3PaymentProps {
  onPrevious: () => void
  onSubmit: () => void
}

export function Step3Payment({ onPrevious, onSubmit }: Step3PaymentProps) {
  const { 
    state, 
    setSelectedBank, 
    setReferenceNumber, 
    setReceiptFile,
    canSubmitOrder 
  } = useCheckout()

  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load bank accounts
  useEffect(() => {
    const accounts = getActiveBankAccounts()
    setBankAccounts(accounts)
  }, [])

  const handleBankSelect = (bank: BankAccount) => {
    setSelectedBank(bank)
  }

  const handleReferenceChange = (value: string) => {
    setReferenceNumber(value)
  }

  const handleReceiptChange = (file: File | null, preview: string | null) => {
    setReceiptFile(file, preview)
  }

  const handleSubmit = async () => {
    if (!canSubmitOrder()) return

    setIsSubmitting(true)
    try {
      await onSubmit()
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Step Header */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <CreditCard className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <CardTitle className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            إتمام الدفع
          </CardTitle>
          <p className="text-slate-300 text-base lg:text-lg">
            اختر البنك وأرفق إيصال التحويل
          </p>
        </CardHeader>
      </Card>

      {/* Bank Selection */}
      <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
        <CardHeader>
          <CardTitle className="text-xl text-white">
            اختر البنك للتحويل
          </CardTitle>
          <p className="text-slate-400">
            اختر البنك الذي تريد التحويل إليه
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {bankAccounts.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {bankAccounts.map((bank) => (
                <BankOptionCard
                  key={bank.id}
                  bank={bank}
                  isSelected={state.selectedBank?.id === bank.id}
                  onSelect={handleBankSelect}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-slate-400">
                لا توجد حسابات بنكية متاحة حالياً
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Details */}
      {state.selectedBank && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Reference Number */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-white">
                <Hash className="h-6 w-6 text-yellow-400" />
                رقم المرجع
              </CardTitle>
              <p className="text-slate-400">
                أدخل رقم المرجع من إيصال التحويل
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reference" className="text-slate-300 font-medium">
                  رقم المرجع *
                </Label>
                <Input
                  id="reference"
                  type="text"
                  placeholder="أدخل رقم المرجع"
                  value={state.referenceNumber}
                  onChange={(e) => handleReferenceChange(e.target.value)}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-500 focus:border-yellow-400"
                />
              </div>
              
              <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
                <h4 className="text-yellow-400 font-medium mb-2">
                  تعليمات مهمة:
                </h4>
                <ul className="text-slate-300 text-sm space-y-1">
                  <li>• تأكد من صحة رقم المرجع</li>
                  <li>• احتفظ بإيصال التحويل الأصلي</li>
                  <li>• سيتم التحقق من التحويل خلال 24 ساعة</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Receipt Upload */}
          <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl text-white">
                <Upload className="h-6 w-6 text-yellow-400" />
                إيصال التحويل
              </CardTitle>
              <p className="text-slate-400">
                ارفع صورة واضحة لإيصال التحويل
              </p>
            </CardHeader>
            <CardContent>
              <UploadReceipt
                file={state.receiptFile}
                preview={state.receiptPreview}
                onFileChange={handleReceiptChange}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Order Summary */}
      {state.selectedBank && (
        <Card className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border-yellow-400/20 shadow-2xl">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-yellow-400 mb-4">
              ملخص نهائي للطلب
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-slate-400 text-sm">المبلغ</p>
                <p className="text-white font-bold text-lg">
                  {formatCurrency(state.amount, state.currency)}
                </p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">البنك المختار</p>
                <p className="text-white font-bold text-lg">
                  {state.selectedBank.name}
                </p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">طريقة الدفع</p>
                <p className="text-white font-bold text-lg">
                  تحويل بنكي
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row gap-4 sm:justify-between">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full sm:w-auto px-6 py-3 text-lg border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
        >
          <ArrowRight className="h-5 w-5 ml-2" />
          السابق
        </Button>

        <Button
          onClick={handleSubmit}
          disabled={!canSubmitOrder() || isSubmitting}
          className={cn(
            "w-full sm:w-auto px-8 py-3 text-lg font-semibold transition-all duration-300",
            canSubmitOrder() && !isSubmitting
              ? "bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 hover:scale-105 shadow-lg hover:shadow-green-500/25"
              : "bg-slate-700 text-slate-400 cursor-not-allowed"
          )}
        >
          {isSubmitting ? "جاري المعالجة..." : `إدفع ${formatCurrency(state.amount, state.currency)}`}
        </Button>
      </div>
    </div>
  )
}
