"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import {
  LayoutDashboard,
  Package,
  FolderOpen,
  Settings,
  BarChart3,
  Users,
  ShoppingCart,
  DollarSign,
  Home,
  ArrowLeft,
  Phone
} from "lucide-react"
import { ProductDashboard } from "@/components/admin/ProductDashboard"
import { ContactPageAdmin } from "@/components/admin/ContactPageAdmin"
import { AdminHeader } from "@/components/admin/AdminHeader"
import { AdminOrderDashboard } from "@/components/admin/AdminOrderDashboard"
import { AdminChatButton } from "@/components/chat/AdminChatButton"
import { formatCurrency } from "@/lib/data/currencies"
import { CurrencyManagement } from "@/components/admin/CurrencyManagement"

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<"overview" | "categories" | "products" | "orders" | "settings" | "contact">("overview")
  const router = useRouter()

  const handleHomeNavigation = () => {
    router.push("/")
  }

  // ## Mock stats data - will be replaced with Supabase queries
  const stats = {
    totalProducts: 15,
    totalCategories: 4,
    totalOrders: 128,
    totalRevenue: 45600,
    activeProducts: 12,
    pendingOrders: 8
  }

  const renderContent = () => {
    switch (activeTab) {
      case "categories":
        return (
          <div className="text-white p-6 lg:p-8">
            <div className="max-w-6xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                  إدارة الفئات
                </h1>
                <p className="text-slate-400 text-lg">إنشاء وتنظيم فئات المنتجات</p>
              </div>

              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-8 lg:p-12 text-center">
                  <FolderOpen className="h-20 w-20 text-slate-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-white mb-4">قريباً</h3>
                  <p className="text-slate-300 text-lg max-w-md mx-auto">
                    نظام إدارة الفئات الجديد قيد التطوير. سيتم إطلاقه قريباً مع ميزات متقدمة.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        )
      case "products":
        return <ProductDashboard />
      case "orders":
        return <AdminOrderDashboard />
      case "contact":
        return <ContactPageAdmin />
      case "settings":
        return (
          <div className="text-white p-6 lg:p-8">
            <div className="max-w-6xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                  الإعدادات
                </h1>
                <p className="text-slate-400 text-lg">تخصيص وإعدادات النظام</p>
              </div>

              <CurrencyManagement />
            </div>
          </div>
        )
      default: // overview
        return (
          <div className="text-white p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              {/* Header */}
              <div className="mb-8 lg:mb-10">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
                  نظرة عامة
                </h1>
                <p className="text-slate-400 text-lg">
                  مرحباً بك في لوحة التحكم - إدارة شاملة للمنتجات والطلبات
                </p>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي المنتجات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalProducts}</p>
                        <p className="text-green-400 text-xs sm:text-sm">
                          {stats.activeProducts} نشط
                        </p>
                      </div>
                      <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">الفئات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalCategories}</p>
                        <p className="text-blue-400 text-xs sm:text-sm">
                          جميعها نشطة
                        </p>
                      </div>
                      <FolderOpen className="h-6 w-6 sm:h-8 sm:w-8 text-green-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي الطلبات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{stats.totalOrders}</p>
                        <p className="text-yellow-400 text-xs sm:text-sm">
                          {stats.pendingOrders} في الانتظار
                        </p>
                      </div>
                      <ShoppingCart className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-3 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-xs sm:text-sm">إجمالي الإيرادات</p>
                        <p className="text-xl sm:text-2xl font-bold text-white">{formatCurrency(stats.totalRevenue, "SDG")}</p>
                      </div>
                      <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-green-400" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      إدارة المنتجات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      إضافة وتعديل وحذف المنتجات مع الحقول والخيارات الديناميكية
                    </p>
                    <Button
                      onClick={() => setActiveTab("products")}
                      className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white"
                    >
                      إدارة المنتجات
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <FolderOpen className="h-5 w-5" />
                      إدارة الفئات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      تنظيم المنتجات في فئات مختلفة لسهولة التصفح والإدارة
                    </p>
                    <Button
                      onClick={() => setActiveTab("categories")}
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                    >
                      إدارة الفئات
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Phone className="h-5 w-5" />
                      إدارة صفحة الاتصال
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      إدارة محتوى صفحة الاتصال والأسئلة الشائعة والنموذج
                    </p>
                    <Button
                      onClick={() => setActiveTab("contact")}
                      className="w-full bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white"
                    >
                      إدارة صفحة الاتصال
                    </Button>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      التقارير والإحصائيات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-slate-300 text-sm">
                      عرض تقارير مفصلة عن المبيعات والطلبات والأداء العام
                    </p>
                    <Button
                      disabled
                      className="w-full bg-slate-600 text-slate-400 cursor-not-allowed"
                    >
                      قريباً
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* System Info */}
              <div className="mt-8">
                <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white">معلومات النظام</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-slate-400">إصدار النظام</p>
                        <p className="text-white font-medium">v1.0.0 Beta</p>
                      </div>
                      <div>
                        <p className="text-slate-400">قاعدة البيانات</p>
                        <p className="text-white font-medium">Supabase (قيد الإعداد)</p>
                      </div>
                      <div>
                        <p className="text-slate-400">آخر تحديث</p>
                        <p className="text-white font-medium">{(() => {
                          const date = new Date()
                          const year = date.getFullYear()
                          const month = String(date.getMonth() + 1).padStart(2, '0')
                          const day = String(date.getDate()).padStart(2, '0')
                          return `${day}/${month}/${year}`
                        })()}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Improved Admin Header */}
      <AdminHeader activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Content */}
      {renderContent()}

      {/* Admin Chat Button - Floating */}
      <AdminChatButton
        userId="admin-demo"
        userName="مدير النظام"
        userEmail="<EMAIL>"
        position="bottom-right"
      />
    </div>
  )
}
