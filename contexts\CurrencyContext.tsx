"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { Currency, CurrencyDisplay } from '@/lib/types'
import { CURRENCIES } from '@/lib/data/currencies'

interface CurrencyContextType {
  selectedCurrency: Currency
  availableCurrencies: CurrencyDisplay[]
  updateCurrency: (currency: Currency) => void
  exchangeRates: Record<string, number>
  isLoading: boolean
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

interface CurrencyProviderProps {
  children: ReactNode
}

// Mock exchange rates - in production this would come from API
const mockExchangeRates: Record<string, number> = {
  'USD': 1,
  'SDG': 450.00,
  'EGP': 30.80
}

export function CurrencyProvider({ children }: CurrencyProviderProps) {
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('USD')
  const [isLoading, setIsLoading] = useState(false)
  
  // Get available currencies from the existing currencies data
  const availableCurrencies: CurrencyDisplay[] = Object.values(CURRENCIES)
  
  // Load saved currency preference from localStorage
  useEffect(() => {
    const savedCurrency = localStorage.getItem('selectedCurrency')
    if (savedCurrency && availableCurrencies.some(c => c.code === savedCurrency)) {
      setSelectedCurrency(savedCurrency as Currency)
    }
  }, [])

  // Update currency and save to localStorage
  const updateCurrency = async (currency: Currency) => {
    setIsLoading(true)
    try {
      setSelectedCurrency(currency)
      localStorage.setItem('selectedCurrency', currency)
      
      // ## TODO: Save to user preferences API when database is integrated
      // await fetch('/api/user/preferences', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     userId: 'user-id', // Get from auth
      //     preferences: {
      //       preferredCurrency: currency,
      //       displayCurrency: currency
      //     }
      //   })
      // })
      
    } catch (error) {
      console.error('Failed to update currency preference:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const value: CurrencyContextType = {
    selectedCurrency,
    availableCurrencies,
    updateCurrency,
    exchangeRates: mockExchangeRates,
    isLoading
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  )
}

export function useCurrency() {
  const context = useContext(CurrencyContext)
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider')
  }
  return context
}

// Helper hook for converting prices
export function useCurrencyConverter() {
  const { selectedCurrency, exchangeRates } = useCurrency()
  
  const convertPrice = (priceUSD: number, targetCurrency?: Currency): number => {
    const currency = targetCurrency || selectedCurrency
    const rate = exchangeRates[currency] || 1
    return priceUSD * rate
  }
  
  const formatPrice = (priceUSD: number, targetCurrency?: Currency): string => {
    const currency = targetCurrency || selectedCurrency
    const convertedPrice = convertPrice(priceUSD, currency)
    const currencyInfo = CURRENCIES[currency]
    
    if (!currencyInfo) {
      return `${convertedPrice.toFixed(2)} ${currency}`
    }
    
    // Format with proper currency symbol and RTL support
    if (currencyInfo.isRTL) {
      return `${convertedPrice.toLocaleString()} ${currencyInfo.symbol}`
    } else {
      return `${currencyInfo.symbol}${convertedPrice.toLocaleString()}`
    }
  }
  
  return {
    convertPrice,
    formatPrice,
    selectedCurrency,
    exchangeRates
  }
}
