"use client"

import {
  ProductOrder,
  OrderStatus,
  OrderFilters,
  OrderListItem,
  AdminOrderStats,
  OrderEvent,
  Currency,
  RevenueConsolidated,
  RevenueCalculationOptions
} from "@/lib/types"
import { getExchangeRate, convertCurrency } from "./exchangeRates"

// ===== LOCALSTORAGE KEYS =====
const PRODUCT_ORDERS_KEY = "product_orders"
const ORDER_DRAFTS_KEY = "order_drafts"

// ===== SUPABASE INTEGRATION PREPARATION =====

/**
 * ## SUPABASE INTEGRATION NOTES:
 * 
 * Database Schema Required:
 * 
 * -- Main orders table
 * CREATE TABLE product_orders (
 *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
 *   user_id UUID REFERENCES auth.users(id),
 *   template_id UUID NOT NULL,
 *   template_name VARCHAR(255) NOT NULL,
 *   template_category VARCHAR(100) NOT NULL,
 *   product_data JSONB NOT NULL,
 *   pricing_data JSONB NOT NULL,
 *   user_details JSONB NOT NULL,
 *   status VARCHAR(20) NOT NULL DEFAULT 'pending',
 *   processing_type VARCHAR(20) NOT NULL DEFAULT 'manual',
 *   admin_notes TEXT,
 *   internal_notes TEXT,
 *   assigned_admin_id UUID REFERENCES auth.users(id),
 *   priority VARCHAR(20) DEFAULT 'normal',
 *   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *   updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *   completed_at TIMESTAMP WITH TIME ZONE
 * );
 * 
 * -- Order timeline/events table
 * CREATE TABLE order_events (
 *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
 *   order_id UUID REFERENCES product_orders(id) ON DELETE CASCADE,
 *   event_type VARCHAR(50) NOT NULL,
 *   description TEXT NOT NULL,
 *   details JSONB,
 *   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *   created_by UUID REFERENCES auth.users(id)
 * );
 * 
 * -- Order attachments table
 * CREATE TABLE order_attachments (
 *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
 *   order_id UUID REFERENCES product_orders(id) ON DELETE CASCADE,
 *   field_name VARCHAR(100) NOT NULL,
 *   file_name VARCHAR(255) NOT NULL,
 *   file_url TEXT NOT NULL,
 *   file_size BIGINT NOT NULL,
 *   mime_type VARCHAR(100) NOT NULL,
 *   uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
 * );
 * 
 * -- Indexes for performance
 * CREATE INDEX idx_product_orders_user_id ON product_orders(user_id);
 * CREATE INDEX idx_product_orders_status ON product_orders(status);
 * CREATE INDEX idx_product_orders_created_at ON product_orders(created_at);
 * CREATE INDEX idx_product_orders_template_id ON product_orders(template_id);
 * CREATE INDEX idx_order_events_order_id ON order_events(order_id);
 * 
 * -- RLS Policies
 * ALTER TABLE product_orders ENABLE ROW LEVEL SECURITY;
 * ALTER TABLE order_events ENABLE ROW LEVEL SECURITY;
 * ALTER TABLE order_attachments ENABLE ROW LEVEL SECURITY;
 * 
 * -- Users can only see their own orders
 * CREATE POLICY "Users can view own orders" ON product_orders
 *   FOR SELECT USING (auth.uid() = user_id);
 * 
 * -- Admins can see all orders
 * CREATE POLICY "Admins can view all orders" ON product_orders
 *   FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
 */

// ===== LOCALSTORAGE FUNCTIONS (TEMPORARY) =====

/**
 * Get all product orders from localStorage
 * ## Supabase Integration: Replace with supabase query
 */
export function getProductOrders(): ProductOrder[] {
  if (typeof window === "undefined") return []

  try {
    const stored = localStorage.getItem(PRODUCT_ORDERS_KEY)
    if (!stored) return []

    const parsed = JSON.parse(stored)
    return parsed.map((order: any) => ({
      ...order,
      createdAt: new Date(order.createdAt),
      updatedAt: new Date(order.updatedAt),
      completedAt: order.completedAt ? new Date(order.completedAt) : undefined,
      timeline: order.timeline.map((event: any) => ({
        ...event,
        createdAt: new Date(event.createdAt)
      }))
    }))
  } catch (error) {
    console.error("Error loading product orders:", error)
    return []
  }
}

/**
 * Save product order to localStorage
 * ## Supabase Integration: Replace with supabase.from('product_orders').insert()
 */
export function saveProductOrder(order: ProductOrder): void {
  if (typeof window === "undefined") return

  try {
    const orders = getProductOrders()
    const existingIndex = orders.findIndex(o => o.id === order.id)
    
    if (existingIndex >= 0) {
      orders[existingIndex] = order
    } else {
      orders.push(order)
    }
    
    localStorage.setItem(PRODUCT_ORDERS_KEY, JSON.stringify(orders))
  } catch (error) {
    console.error("Error saving product order:", error)
    throw new Error("فشل في حفظ الطلب")
  }
}

/**
 * Get product order by ID
 * ## Supabase Integration: Replace with supabase.from('product_orders').select().eq('id', orderId).single()
 */
export function getProductOrderById(orderId: string): ProductOrder | null {
  const orders = getProductOrders()
  return orders.find(order => order.id === orderId) || null
}

/**
 * Update product order status
 * ## Supabase Integration: Replace with supabase.from('product_orders').update().eq('id', orderId)
 */
export function updateProductOrderStatus(
  orderId: string, 
  status: OrderStatus, 
  adminNotes?: string,
  adminId?: string
): void {
  if (typeof window === "undefined") return

  try {
    const orders = getProductOrders()
    const orderIndex = orders.findIndex(order => order.id === orderId)

    if (orderIndex !== -1) {
      const order = orders[orderIndex]
      
      // Create status change event
      const statusEvent: OrderEvent = {
        id: `event_${Date.now()}`,
        type: "status_change",
        description: `تم تغيير حالة الطلب إلى "${getStatusLabel(status)}"`,
        details: {
          previousStatus: order.status,
          newStatus: status,
          adminNotes
        },
        createdAt: new Date(),
        createdBy: adminId || "admin"
      }

      // Update order
      orders[orderIndex] = {
        ...order,
        status,
        updatedAt: new Date(),
        completedAt: status === "completed" ? new Date() : order.completedAt,
        adminNotes: adminNotes || order.adminNotes,
        assignedAdminId: adminId || order.assignedAdminId,
        timeline: [...order.timeline, statusEvent]
      }

      localStorage.setItem(PRODUCT_ORDERS_KEY, JSON.stringify(orders))
    }
  } catch (error) {
    console.error("Error updating order status:", error)
    throw new Error("فشل في تحديث حالة الطلب")
  }
}

/**
 * Get filtered orders for admin dashboard
 * ## Supabase Integration: Replace with complex supabase query with filters
 */
export function getFilteredOrders(filters: OrderFilters): OrderListItem[] {
  const orders = getProductOrders()
  
  let filtered = orders

  // Apply status filter
  if (filters.status && filters.status.length > 0) {
    filtered = filtered.filter(order => filters.status!.includes(order.status))
  }

  // Apply processing type filter
  if (filters.processingType && filters.processingType.length > 0) {
    filtered = filtered.filter(order => filters.processingType!.includes(order.processingType))
  }

  // Apply date range filter
  if (filters.dateRange) {
    filtered = filtered.filter(order => {
      const orderDate = new Date(order.createdAt)
      return orderDate >= filters.dateRange!.start && orderDate <= filters.dateRange!.end
    })
  }

  // Apply template filter
  if (filters.templateId) {
    filtered = filtered.filter(order => order.templateId === filters.templateId)
  }

  // Apply search filter
  if (filters.search) {
    const searchLower = filters.search.toLowerCase()
    filtered = filtered.filter(order => 
      order.id.toLowerCase().includes(searchLower) ||
      order.templateName.toLowerCase().includes(searchLower) ||
      order.userDetails.fullName.toLowerCase().includes(searchLower) ||
      order.userDetails.email.toLowerCase().includes(searchLower)
    )
  }

  // Convert to OrderListItem format
  return filtered.map(order => ({
    id: order.id,
    templateName: order.templateName,
    customerName: order.userDetails.fullName,
    customerEmail: order.userDetails.email,
    totalPrice: order.pricing.totalPrice,
    currency: order.pricing.currency,
    status: order.status,
    processingType: order.processingType,
    createdAt: order.createdAt,
    priority: order.priority
  }))
}

/**
 * Get admin order statistics
 * ## Supabase Integration: Replace with aggregation queries
 */
export function getAdminOrderStats(): AdminOrderStats {
  const orders = getProductOrders()
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const stats: AdminOrderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === "pending").length,
    processing: orders.filter(o => o.status === "processing").length,
    completed: orders.filter(o => o.status === "completed").length,
    failed: orders.filter(o => o.status === "failed").length,
    cancelled: orders.filter(o => o.status === "cancelled").length,
    todayOrders: orders.filter(o => new Date(o.createdAt) >= today).length,
    avgProcessingTime: calculateAvgProcessingTime(orders),
    revenue: calculateRevenue(orders)
  }

  return stats
}

/**
 * Get user's orders
 * ## Supabase Integration: Replace with RLS-protected query
 */
export function getUserOrders(userId?: string): ProductOrder[] {
  // For now, return all orders since we don't have user authentication
  // In Supabase, this will be automatically filtered by RLS
  return getProductOrders()
}

// ===== HELPER FUNCTIONS =====

function getStatusLabel(status: OrderStatus): string {
  const labels: Record<OrderStatus, string> = {
    pending: "في الانتظار",
    processing: "قيد المعالجة", 
    completed: "مكتمل",
    failed: "فشل",
    cancelled: "ملغي"
  }
  return labels[status]
}

function calculateAvgProcessingTime(orders: ProductOrder[]): number {
  const completedOrders = orders.filter(o => o.status === "completed" && o.completedAt)
  
  if (completedOrders.length === 0) return 0

  const totalTime = completedOrders.reduce((sum, order) => {
    const processingTime = new Date(order.completedAt!).getTime() - new Date(order.createdAt).getTime()
    return sum + processingTime
  }, 0)

  return totalTime / completedOrders.length / (1000 * 60 * 60) // Convert to hours
}

function calculateRevenue(orders: ProductOrder[]): Record<string, number> {
  const revenue: Record<string, number> = {}

  orders
    .filter(o => o.status === "completed")
    .forEach(order => {
      const currency = order.pricing.currency
      revenue[currency] = (revenue[currency] || 0) + order.pricing.totalPrice
    })

  return revenue
}

/**
 * Enhanced revenue calculation with multi-currency consolidation
 */
export async function calculateConsolidatedRevenue(
  orders: ProductOrder[],
  options: RevenueCalculationOptions = {}
): Promise<RevenueConsolidated> {
  const {
    startDate,
    endDate,
    primaryCurrency = 'USD',
    includePending = false,
    groupByCurrency = true
  } = options

  // Filter orders by date range and status
  let filteredOrders = orders.filter(order => {
    const orderDate = new Date(order.createdAt)

    // Date range filter
    if (startDate && orderDate < startDate) return false
    if (endDate && orderDate > endDate) return false

    // Status filter
    if (!includePending && order.status !== 'completed') return false

    return true
  })

  // Calculate revenue by original currency
  const revenueByCurrency: Record<Currency, number> = {}
  const exchangeRatesUsed: Record<string, number> = {}

  for (const order of filteredOrders) {
    const currency = order.pricing.currency
    const amount = order.pricing.totalPrice

    revenueByCurrency[currency] = (revenueByCurrency[currency] || 0) + amount
  }

  // Convert all revenues to primary currency
  let totalRevenue = 0

  for (const [currency, amount] of Object.entries(revenueByCurrency)) {
    if (currency === primaryCurrency) {
      totalRevenue += amount
      exchangeRatesUsed[`${currency}_to_${primaryCurrency}`] = 1.0
    } else {
      try {
        const exchangeRate = await getExchangeRate(currency, primaryCurrency)
        const convertedAmount = amount * exchangeRate
        totalRevenue += convertedAmount
        exchangeRatesUsed[`${currency}_to_${primaryCurrency}`] = exchangeRate
      } catch (error) {
        console.warn(`Failed to get exchange rate for ${currency} to ${primaryCurrency}:`, error)
        // Skip this currency in total calculation
      }
    }
  }

  return {
    primaryCurrency,
    totalRevenue,
    revenueByCurrency,
    exchangeRatesUsed,
    calculatedAt: new Date(),
    periodStart: startDate,
    periodEnd: endDate
  }
}

/**
 * Get revenue breakdown by time period
 */
export async function getRevenueBreakdown(
  orders: ProductOrder[],
  groupBy: 'day' | 'week' | 'month' | 'year' = 'month',
  primaryCurrency: Currency = 'USD'
): Promise<Array<{
  period: string
  revenue: number
  orders: number
  revenueByCurrency: Record<Currency, number>
}>> {
  const groups: Record<string, {
    revenue: number
    orders: number
    revenueByCurrency: Record<Currency, number>
  }> = {}

  for (const order of orders.filter(o => o.status === 'completed')) {
    const date = new Date(order.createdAt)
    let periodKey: string

    switch (groupBy) {
      case 'day':
        periodKey = date.toISOString().split('T')[0]
        break
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        periodKey = weekStart.toISOString().split('T')[0]
        break
      case 'month':
        periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      case 'year':
        periodKey = String(date.getFullYear())
        break
    }

    if (!groups[periodKey]) {
      groups[periodKey] = {
        revenue: 0,
        orders: 0,
        revenueByCurrency: {}
      }
    }

    const currency = order.pricing.currency
    const amount = order.pricing.totalPrice

    groups[periodKey].orders += 1
    groups[periodKey].revenueByCurrency[currency] =
      (groups[periodKey].revenueByCurrency[currency] || 0) + amount

    // Convert to primary currency for total
    try {
      if (currency === primaryCurrency) {
        groups[periodKey].revenue += amount
      } else {
        const exchangeRate = await getExchangeRate(currency, primaryCurrency)
        groups[periodKey].revenue += amount * exchangeRate
      }
    } catch (error) {
      console.warn(`Failed to convert ${currency} to ${primaryCurrency}:`, error)
    }
  }

  // Convert to array and sort by period
  return Object.entries(groups)
    .map(([period, data]) => ({
      period,
      ...data
    }))
    .sort((a, b) => a.period.localeCompare(b.period))
}

/**
 * Get revenue summary statistics
 */
export async function getRevenueSummary(
  orders: ProductOrder[],
  primaryCurrency: Currency = 'USD'
): Promise<{
  total: { orders: number; revenue: number; revenueByCurrency: Record<Currency, number> }
  today: { orders: number; revenue: number; revenueByCurrency: Record<Currency, number> }
  thisMonth: { orders: number; revenue: number; revenueByCurrency: Record<Currency, number> }
  thisYear: { orders: number; revenue: number; revenueByCurrency: Record<Currency, number> }
}> {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const thisYear = new Date(now.getFullYear(), 0, 1)

  const periods = [
    { key: 'total', filter: () => true },
    { key: 'today', filter: (date: Date) => date >= today },
    { key: 'thisMonth', filter: (date: Date) => date >= thisMonth },
    { key: 'thisYear', filter: (date: Date) => date >= thisYear }
  ]

  const summary: any = {}

  for (const period of periods) {
    const filteredOrders = orders.filter(order =>
      order.status === 'completed' && period.filter(new Date(order.createdAt))
    )

    const consolidated = await calculateConsolidatedRevenue(filteredOrders, {
      primaryCurrency,
      includePending: false
    })

    summary[period.key] = {
      orders: filteredOrders.length,
      revenue: consolidated.totalRevenue,
      revenueByCurrency: consolidated.revenueByCurrency
    }
  }

  return summary
}

/**
 * Delete order (admin only)
 * ## Supabase Integration: Replace with supabase.from('product_orders').delete().eq('id', orderId)
 */
export function deleteProductOrder(orderId: string): void {
  if (typeof window === "undefined") return

  try {
    const orders = getProductOrders()
    const filteredOrders = orders.filter(order => order.id !== orderId)
    localStorage.setItem(PRODUCT_ORDERS_KEY, JSON.stringify(filteredOrders))
  } catch (error) {
    console.error("Error deleting order:", error)
    throw new Error("فشل في حذف الطلب")
  }
}
